# Test script to verify PWA functionality
import requests
import json
import os

def test_pwa_endpoints():
    """Test PWA-specific endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing PWA Endpoints...")
    print("=" * 50)
    
    # Test main app
    try:
        response = requests.get(base_url)
        if response.status_code == 200:
            print("✅ Main app is running")
        else:
            print(f"❌ Main app failed: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to app. Make sure it's running on port 5000")
        return False
    
    # Test manifest
    try:
        response = requests.get(f"{base_url}/manifest.json")
        if response.status_code == 200:
            manifest = response.json()
            print(f"✅ Manifest loaded: {manifest.get('name', 'Unknown')}")
        else:
            print(f"❌ Manifest failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Manifest error: {e}")
    
    # Test service worker
    try:
        response = requests.get(f"{base_url}/sw.js")
        if response.status_code == 200:
            print("✅ Service Worker accessible")
        else:
            print(f"❌ Service Worker failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Service Worker error: {e}")
    
    # Test offline page
    try:
        response = requests.get(f"{base_url}/offline.html")
        if response.status_code == 200:
            print("✅ Offline page accessible")
        else:
            print(f"❌ Offline page failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Offline page error: {e}")
    
    # Test PWA status API
    try:
        response = requests.get(f"{base_url}/api/pwa/status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ PWA Status API: {status}")
        else:
            print(f"❌ PWA Status API failed: {response.status_code}")
    except Exception as e:
        print(f"❌ PWA Status API error: {e}")
    
    print("=" * 50)
    return True

def check_pwa_files():
    """Check if PWA files exist"""
    print("\n📁 Checking PWA Files...")
    print("=" * 50)
    
    files_to_check = [
        "static/manifest.json",
        "static/sw.js",
        "static/js/pwa.js",
        "static/css/pwa.css",
        "templates/offline.html",
        "pwa_routes.py",
        "app_pwa.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            all_exist = False
    
    print("=" * 50)
    return all_exist

def show_testing_instructions():
    """Show instructions for testing PWA"""
    print("\n📱 PWA Testing Instructions:")
    print("=" * 50)
    print("1. 🚀 START THE APP:")
    print("   Run: python app_pwa.py")
    print("")
    print("2. 🌐 OPEN IN BROWSER:")
    print("   Desktop: http://localhost:5000")
    print("   Mobile: http://[your-ip]:5000")
    print("")
    print("3. 📱 TEST PWA FEATURES:")
    print("   • Look for 'Install App' button")
    print("   • Check browser address bar for install icon")
    print("   • Try Developer Tools > Application > Manifest")
    print("   • Test offline: DevTools > Network > Offline checkbox")
    print("")
    print("4. 📲 MOBILE TESTING:")
    print("   • Open in Chrome/Safari on phone")
    print("   • Look for 'Add to Home Screen' prompt")
    print("   • Install and test as standalone app")
    print("")
    print("5. 🔧 DEVELOPER TOOLS:")
    print("   • F12 > Application tab")
    print("   • Check Service Workers section")
    print("   • Check Manifest section")
    print("   • Check Storage > Cache Storage")
    print("=" * 50)

def main():
    print("🔍 PWA Test Suite for E-Pass System")
    print("=" * 50)
    
    # Check files first
    files_ok = check_pwa_files()
    
    if not files_ok:
        print("\n❌ Some PWA files are missing. Please ensure all files are created.")
        return
    
    # Show testing instructions
    show_testing_instructions()
    
    # Ask if user wants to test endpoints
    print("\n🧪 Do you want to test PWA endpoints now?")
    print("(Make sure app_pwa.py is running first)")
    
    choice = input("Test endpoints? (y/n): ").lower().strip()
    
    if choice == 'y':
        test_pwa_endpoints()
    
    print("\n✨ PWA testing complete!")
    print("Run 'python app_pwa.py' to start your PWA-enabled app!")

if __name__ == "__main__":
    main()
