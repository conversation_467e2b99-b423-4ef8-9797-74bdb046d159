from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
import sqlite3
import hashlib
import secrets
from werkzeug.utils import secure_filename
import re

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'smart_water_system_2024'
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class DatabaseManager:
    def __init__(self):
        self.db_name = 'smart_water.db'
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                phone_number TEXT UNIQUE NOT NULL,
                email TEXT,
                address TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                user_type TEXT DEFAULT 'customer',
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Water meters table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS water_meters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_number TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                location TEXT NOT NULL,
                installation_date DATE,
                last_reading REAL DEFAULT 0,
                status TEXT DEFAULT 'active',
                meter_type TEXT DEFAULT 'smart',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Water consumption readings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS water_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_id INTEGER,
                reading_value REAL NOT NULL,
                reading_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reading_type TEXT DEFAULT 'automatic',
                consumption REAL,
                cost REAL,
                FOREIGN KEY (meter_id) REFERENCES water_meters (id)
            )
        ''')
        
        # Bills table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                meter_id INTEGER,
                billing_period TEXT NOT NULL,
                consumption REAL NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                status TEXT DEFAULT 'pending',
                payment_date TIMESTAMP,
                payment_method TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (meter_id) REFERENCES water_meters (id)
            )
        ''')
        
        # Payments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bill_id INTEGER,
                user_id INTEGER,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                transaction_id TEXT,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed',
                FOREIGN KEY (bill_id) REFERENCES bills (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Leak alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leak_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_id INTEGER,
                user_id INTEGER,
                alert_type TEXT NOT NULL,
                severity TEXT DEFAULT 'medium',
                description TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP,
                FOREIGN KEY (meter_id) REFERENCES water_meters (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user
        self.create_default_admin()
    
    def create_default_admin(self):
        """Create default admin user if not exists"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM admin_users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'System Administrator', 'super_admin'))
            conn.commit()
        
        conn.close()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_name)

class SmartWaterSystem:
    def __init__(self):
        self.db = DatabaseManager()
        self.water_rate_per_cubic_meter = 500  # RWF per cubic meter
        self.service_fee = 1000  # Monthly service fee in RWF
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, full_name, phone_number, email, address, id_number, password):
        """Register new water customer"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute('SELECT id FROM users WHERE phone_number = ? OR id_number = ?', 
                         (phone_number, id_number))
            if cursor.fetchone():
                return False, "User with this phone number or ID already exists"
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (full_name, phone_number, email, address, id_number, password_hash)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (full_name, phone_number, email, address, id_number, password_hash))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"User registered successfully with ID: {user_id}"
            
        except Exception as e:
            return False, f"Registration failed: {str(e)}"
    
    def authenticate_user(self, phone_number, password):
        """Authenticate user login"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT id, full_name, phone_number, email, user_type 
                FROM users 
                WHERE phone_number = ? AND password_hash = ? AND is_active = 1
            ''', (phone_number, password_hash))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                return True, {
                    'id': user[0],
                    'full_name': user[1],
                    'phone_number': user[2],
                    'email': user[3],
                    'user_type': user[4]
                }
            else:
                return False, "Invalid credentials"
                
        except Exception as e:
            return False, f"Authentication failed: {str(e)}"
    
    def install_water_meter(self, user_id, meter_number, location):
        """Install water meter for user"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if meter number already exists
            cursor.execute('SELECT id FROM water_meters WHERE meter_number = ?', (meter_number,))
            if cursor.fetchone():
                return False, "Meter number already exists"
            
            cursor.execute('''
                INSERT INTO water_meters (meter_number, user_id, location, installation_date)
                VALUES (?, ?, ?, ?)
            ''', (meter_number, user_id, location, datetime.date.today()))
            
            meter_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"Water meter installed successfully with ID: {meter_id}"
            
        except Exception as e:
            return False, f"Meter installation failed: {str(e)}"

    def record_water_reading(self, meter_id, reading_value, reading_type='automatic'):
        """Record water meter reading"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get previous reading
            cursor.execute('''
                SELECT reading_value FROM water_readings
                WHERE meter_id = ?
                ORDER BY reading_date DESC LIMIT 1
            ''', (meter_id,))

            previous_reading = cursor.fetchone()
            previous_value = previous_reading[0] if previous_reading else 0

            # Calculate consumption
            consumption = max(0, reading_value - previous_value)
            cost = consumption * self.water_rate_per_cubic_meter

            cursor.execute('''
                INSERT INTO water_readings (meter_id, reading_value, reading_type, consumption, cost)
                VALUES (?, ?, ?, ?, ?)
            ''', (meter_id, reading_value, reading_type, consumption, cost))

            # Update meter's last reading
            cursor.execute('''
                UPDATE water_meters SET last_reading = ? WHERE id = ?
            ''', (reading_value, meter_id))

            conn.commit()
            conn.close()

            return True, f"Reading recorded successfully. Consumption: {consumption} m³, Cost: {cost} RWF"

        except Exception as e:
            return False, f"Failed to record reading: {str(e)}"

    def generate_monthly_bill(self, user_id, meter_id, billing_period):
        """Generate monthly water bill"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get total consumption for the billing period
            cursor.execute('''
                SELECT SUM(consumption), SUM(cost) FROM water_readings
                WHERE meter_id = ? AND DATE(reading_date) LIKE ?
            ''', (meter_id, f"{billing_period}%"))

            result = cursor.fetchone()
            total_consumption = result[0] if result[0] else 0
            water_cost = result[1] if result[1] else 0

            # Calculate total amount (water cost + service fee)
            total_amount = water_cost + self.service_fee

            # Set due date (30 days from now)
            due_date = datetime.date.today() + datetime.timedelta(days=30)

            cursor.execute('''
                INSERT INTO bills (user_id, meter_id, billing_period, consumption, amount, due_date)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, meter_id, billing_period, total_consumption, total_amount, due_date))

            bill_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return True, {
                'bill_id': bill_id,
                'consumption': total_consumption,
                'amount': total_amount,
                'due_date': due_date.isoformat()
            }

        except Exception as e:
            return False, f"Failed to generate bill: {str(e)}"

    def process_payment(self, bill_id, user_id, amount, payment_method, transaction_id=None):
        """Process bill payment"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Verify bill exists and belongs to user
            cursor.execute('''
                SELECT amount, status FROM bills
                WHERE id = ? AND user_id = ?
            ''', (bill_id, user_id))

            bill = cursor.fetchone()
            if not bill:
                return False, "Bill not found"

            if bill[1] == 'paid':
                return False, "Bill already paid"

            if amount < bill[0]:
                return False, f"Insufficient amount. Required: {bill[0]} RWF"

            # Record payment
            cursor.execute('''
                INSERT INTO payments (bill_id, user_id, amount, payment_method, transaction_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (bill_id, user_id, amount, payment_method, transaction_id))

            # Update bill status
            cursor.execute('''
                UPDATE bills SET status = 'paid', payment_date = CURRENT_TIMESTAMP, payment_method = ?
                WHERE id = ?
            ''', (payment_method, bill_id))

            conn.commit()
            conn.close()

            return True, "Payment processed successfully"

        except Exception as e:
            return False, f"Payment failed: {str(e)}"

    def detect_leak(self, meter_id, threshold_consumption=50):
        """Detect potential water leaks based on consumption patterns"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get recent readings (last 24 hours)
            cursor.execute('''
                SELECT consumption FROM water_readings
                WHERE meter_id = ? AND reading_date >= datetime('now', '-1 day')
                ORDER BY reading_date DESC
            ''', (meter_id,))

            readings = cursor.fetchall()

            if len(readings) < 2:
                return False, "Insufficient data for leak detection"

            # Check for unusual consumption patterns
            recent_consumption = sum([r[0] for r in readings])

            if recent_consumption > threshold_consumption:
                # Get user and meter info
                cursor.execute('''
                    SELECT u.id, u.full_name, u.phone_number, m.meter_number, m.location
                    FROM users u JOIN water_meters m ON u.id = m.user_id
                    WHERE m.id = ?
                ''', (meter_id,))

                user_info = cursor.fetchone()

                if user_info:
                    # Create leak alert
                    cursor.execute('''
                        INSERT INTO leak_alerts (meter_id, user_id, alert_type, severity, description)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (meter_id, user_info[0], 'high_consumption', 'high',
                          f"Unusual consumption detected: {recent_consumption} m³ in 24 hours"))

                    conn.commit()
                    conn.close()

                    return True, {
                        'user_name': user_info[1],
                        'phone': user_info[2],
                        'meter_number': user_info[3],
                        'location': user_info[4],
                        'consumption': recent_consumption
                    }

            conn.close()
            return False, "No leak detected"

        except Exception as e:
            return False, f"Leak detection failed: {str(e)}"

# Initialize the system
water_system = SmartWaterSystem()

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        full_name = data.get('full_name')
        phone_number = data.get('phone_number')
        email = data.get('email', '')
        address = data.get('address')
        id_number = data.get('id_number')
        password = data.get('password')

        if not all([full_name, phone_number, address, id_number, password]):
            return jsonify({'success': False, 'message': 'All required fields must be filled'}), 400

        success, message = water_system.register_user(full_name, phone_number, email, address, id_number, password)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/login', methods=['POST'])
def login_user():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        password = data.get('password')

        if not all([phone_number, password]):
            return jsonify({'success': False, 'message': 'Phone number and password are required'}), 400

        success, result = water_system.authenticate_user(phone_number, password)

        if success:
            return jsonify({'success': True, 'user': result})
        else:
            return jsonify({'success': False, 'message': result}), 401

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/meter/install', methods=['POST'])
def install_meter():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        meter_number = data.get('meter_number')
        location = data.get('location')

        if not all([user_id, meter_number, location]):
            return jsonify({'success': False, 'message': 'All fields are required'}), 400

        success, message = water_system.install_water_meter(user_id, meter_number, location)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/reading/record', methods=['POST'])
def record_reading():
    try:
        data = request.get_json()
        meter_id = data.get('meter_id')
        reading_value = data.get('reading_value')
        reading_type = data.get('reading_type', 'manual')

        if not all([meter_id, reading_value]):
            return jsonify({'success': False, 'message': 'Meter ID and reading value are required'}), 400

        success, message = water_system.record_water_reading(meter_id, float(reading_value), reading_type)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/bill/generate', methods=['POST'])
def generate_bill():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        meter_id = data.get('meter_id')
        billing_period = data.get('billing_period')  # Format: YYYY-MM

        if not all([user_id, meter_id, billing_period]):
            return jsonify({'success': False, 'message': 'All fields are required'}), 400

        success, result = water_system.generate_monthly_bill(user_id, meter_id, billing_period)

        if success:
            return jsonify({'success': True, 'bill': result})
        else:
            return jsonify({'success': False, 'message': result})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/payment/process', methods=['POST'])
def process_payment():
    try:
        data = request.get_json()
        bill_id = data.get('bill_id')
        user_id = data.get('user_id')
        amount = data.get('amount')
        payment_method = data.get('payment_method')
        transaction_id = data.get('transaction_id')

        if not all([bill_id, user_id, amount, payment_method]):
            return jsonify({'success': False, 'message': 'All required fields must be provided'}), 400

        success, message = water_system.process_payment(bill_id, user_id, float(amount), payment_method, transaction_id)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/leak/detect/<int:meter_id>', methods=['GET'])
def detect_leak(meter_id):
    try:
        success, result = water_system.detect_leak(meter_id)

        if success:
            return jsonify({'success': True, 'leak_detected': True, 'details': result})
        else:
            return jsonify({'success': True, 'leak_detected': False, 'message': result})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/user/dashboard/<int:user_id>', methods=['GET'])
def get_user_dashboard(user_id):
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        # Get user's meters
        cursor.execute('''
            SELECT id, meter_number, location, last_reading, status
            FROM water_meters WHERE user_id = ?
        ''', (user_id,))
        meters = cursor.fetchall()

        # Get latest bill
        cursor.execute('''
            SELECT consumption, amount, due_date, status
            FROM bills WHERE user_id = ?
            ORDER BY created_at DESC LIMIT 1
        ''', (user_id,))
        latest_bill = cursor.fetchone()

        # Get recent readings
        cursor.execute('''
            SELECT wr.reading_value, wr.consumption, wr.reading_date, wm.meter_number
            FROM water_readings wr
            JOIN water_meters wm ON wr.meter_id = wm.id
            WHERE wm.user_id = ?
            ORDER BY wr.reading_date DESC LIMIT 10
        ''', (user_id,))
        recent_readings = cursor.fetchall()

        conn.close()

        dashboard_data = {
            'meters': [{'id': m[0], 'number': m[1], 'location': m[2], 'last_reading': m[3], 'status': m[4]} for m in meters],
            'latest_bill': {
                'consumption': latest_bill[0] if latest_bill else 0,
                'amount': latest_bill[1] if latest_bill else 0,
                'due_date': latest_bill[2] if latest_bill else None,
                'status': latest_bill[3] if latest_bill else 'none'
            } if latest_bill else None,
            'recent_readings': [{'value': r[0], 'consumption': r[1], 'date': r[2], 'meter': r[3]} for r in recent_readings]
        }

        return jsonify({'success': True, 'dashboard': dashboard_data})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/stats', methods=['GET'])
def get_admin_stats():
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        # Total users
        cursor.execute('SELECT COUNT(*) FROM users WHERE is_active = 1')
        total_users = cursor.fetchone()[0]

        # Total meters
        cursor.execute('SELECT COUNT(*) FROM water_meters WHERE status = "active"')
        total_meters = cursor.fetchone()[0]

        # Pending bills
        cursor.execute('SELECT COUNT(*) FROM bills WHERE status = "pending"')
        pending_bills = cursor.fetchone()[0]

        # Active leak alerts
        cursor.execute('SELECT COUNT(*) FROM leak_alerts WHERE status = "active"')
        active_alerts = cursor.fetchone()[0]

        # Monthly revenue
        cursor.execute('''
            SELECT SUM(amount) FROM bills
            WHERE status = "paid" AND DATE(created_at) >= DATE('now', 'start of month')
        ''')
        monthly_revenue = cursor.fetchone()[0] or 0

        # Water consumption this month
        cursor.execute('''
            SELECT SUM(consumption) FROM water_readings
            WHERE DATE(reading_date) >= DATE('now', 'start of month')
        ''')
        monthly_consumption = cursor.fetchone()[0] or 0

        conn.close()

        stats = {
            'total_users': total_users,
            'total_meters': total_meters,
            'pending_bills': pending_bills,
            'active_alerts': active_alerts,
            'monthly_revenue': monthly_revenue,
            'monthly_consumption': monthly_consumption
        }

        return jsonify({'success': True, 'stats': stats})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/users', methods=['GET'])
def get_all_users():
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT u.id, u.full_name, u.phone_number, u.email, u.address,
                   u.created_at, u.is_active,
                   COUNT(wm.id) as meter_count
            FROM users u
            LEFT JOIN water_meters wm ON u.id = wm.user_id
            GROUP BY u.id
            ORDER BY u.created_at DESC
        ''')

        users = cursor.fetchall()
        conn.close()

        user_list = []
        for user in users:
            user_list.append({
                'id': user[0],
                'full_name': user[1],
                'phone_number': user[2],
                'email': user[3],
                'address': user[4],
                'created_at': user[5],
                'is_active': user[6],
                'meter_count': user[7]
            })

        return jsonify({'success': True, 'users': user_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/bills', methods=['GET'])
def get_all_bills():
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT b.id, b.billing_period, b.consumption, b.amount, b.due_date,
                   b.status, b.created_at, u.full_name, u.phone_number, wm.meter_number
            FROM bills b
            JOIN users u ON b.user_id = u.id
            JOIN water_meters wm ON b.meter_id = wm.id
            ORDER BY b.created_at DESC
        ''')

        bills = cursor.fetchall()
        conn.close()

        bill_list = []
        for bill in bills:
            bill_list.append({
                'id': bill[0],
                'billing_period': bill[1],
                'consumption': bill[2],
                'amount': bill[3],
                'due_date': bill[4],
                'status': bill[5],
                'created_at': bill[6],
                'customer_name': bill[7],
                'customer_phone': bill[8],
                'meter_number': bill[9]
            })

        return jsonify({'success': True, 'bills': bill_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/alerts', methods=['GET'])
def get_leak_alerts():
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT la.id, la.alert_type, la.severity, la.description, la.status,
                   la.created_at, u.full_name, u.phone_number, wm.meter_number, wm.location
            FROM leak_alerts la
            JOIN users u ON la.user_id = u.id
            JOIN water_meters wm ON la.meter_id = wm.id
            WHERE la.status = 'active'
            ORDER BY la.created_at DESC
        ''')

        alerts = cursor.fetchall()
        conn.close()

        alert_list = []
        for alert in alerts:
            alert_list.append({
                'id': alert[0],
                'alert_type': alert[1],
                'severity': alert[2],
                'description': alert[3],
                'status': alert[4],
                'created_at': alert[5],
                'customer_name': alert[6],
                'customer_phone': alert[7],
                'meter_number': alert[8],
                'location': alert[9]
            })

        return jsonify({'success': True, 'alerts': alert_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/login', methods=['POST'])
def admin_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not all([username, password]):
            return jsonify({'success': False, 'message': 'Username and password are required'}), 400

        # Simple admin authentication (you can enhance this)
        if username == 'admin' and password == 'admin123':
            return jsonify({
                'success': True,
                'admin': {
                    'username': username,
                    'role': 'super_admin',
                    'full_name': 'System Administrator'
                }
            })
        else:
            return jsonify({'success': False, 'message': 'Invalid admin credentials'}), 401

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/meters', methods=['GET'])
def get_all_meters():
    try:
        conn = water_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT wm.id, wm.meter_number, wm.location, wm.installation_date,
                   wm.last_reading, wm.status, u.full_name, u.phone_number
            FROM water_meters wm
            JOIN users u ON wm.user_id = u.id
            ORDER BY wm.installation_date DESC
        ''')

        meters = cursor.fetchall()
        conn.close()

        meter_list = []
        for meter in meters:
            meter_list.append({
                'id': meter[0],
                'meter_number': meter[1],
                'location': meter[2],
                'installation_date': meter[3],
                'last_reading': meter[4],
                'status': meter[5],
                'customer_name': meter[6],
                'customer_phone': meter[7]
            })

        return jsonify({'success': True, 'meters': meter_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

# File upload route for documents
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    try:
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except FileNotFoundError:
        return jsonify({'success': False, 'message': 'File not found'}), 404

# PWA routes
@app.route('/manifest.json')
def manifest():
    return {
        "name": "Smart Water Distribution System",
        "short_name": "SmartWater",
        "description": "Smart water management with IoT monitoring and mobile billing",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#2563eb",
        "theme_color": "#2563eb",
        "icons": [
            {
                "src": "static/images/icon-192.png",
                "sizes": "192x192",
                "type": "image/png"
            },
            {
                "src": "static/images/icon-512.png",
                "sizes": "512x512",
                "type": "image/png"
            }
        ]
    }

@app.route('/sw.js')
def service_worker():
    return send_from_directory('static', 'sw.js')

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
