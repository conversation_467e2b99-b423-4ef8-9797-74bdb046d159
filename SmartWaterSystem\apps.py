from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
import sqlite3
import hashlib
import secrets
from werkzeug.utils import secure_filename
import re

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'smart_water_system_2024'
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class DatabaseManager:
    def __init__(self):
        self.db_name = 'smart_water.db'
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                phone_number TEXT UNIQUE NOT NULL,
                email TEXT,
                address TEXT NOT NULL,
                id_number TEXT UNIQUE NOT NULL,
                user_type TEXT DEFAULT 'customer',
                password_hash TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Water meters table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS water_meters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_number TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                location TEXT NOT NULL,
                installation_date DATE,
                last_reading REAL DEFAULT 0,
                status TEXT DEFAULT 'active',
                meter_type TEXT DEFAULT 'smart',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Water consumption readings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS water_readings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_id INTEGER,
                reading_value REAL NOT NULL,
                reading_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reading_type TEXT DEFAULT 'automatic',
                consumption REAL,
                cost REAL,
                FOREIGN KEY (meter_id) REFERENCES water_meters (id)
            )
        ''')
        
        # Bills table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                meter_id INTEGER,
                billing_period TEXT NOT NULL,
                consumption REAL NOT NULL,
                amount REAL NOT NULL,
                due_date DATE NOT NULL,
                status TEXT DEFAULT 'pending',
                payment_date TIMESTAMP,
                payment_method TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (meter_id) REFERENCES water_meters (id)
            )
        ''')
        
        # Payments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                bill_id INTEGER,
                user_id INTEGER,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                transaction_id TEXT,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed',
                FOREIGN KEY (bill_id) REFERENCES bills (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Leak alerts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS leak_alerts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                meter_id INTEGER,
                user_id INTEGER,
                alert_type TEXT NOT NULL,
                severity TEXT DEFAULT 'medium',
                description TEXT,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                resolved_at TIMESTAMP,
                FOREIGN KEY (meter_id) REFERENCES water_meters (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user
        self.create_default_admin()
    
    def create_default_admin(self):
        """Create default admin user if not exists"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM admin_users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'System Administrator', 'super_admin'))
            conn.commit()
        
        conn.close()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_name)

class SmartWaterSystem:
    def __init__(self):
        self.db = DatabaseManager()
        self.water_rate_per_cubic_meter = 500  # RWF per cubic meter
        self.service_fee = 1000  # Monthly service fee in RWF
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def register_user(self, full_name, phone_number, email, address, id_number, password):
        """Register new water customer"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute('SELECT id FROM users WHERE phone_number = ? OR id_number = ?', 
                         (phone_number, id_number))
            if cursor.fetchone():
                return False, "User with this phone number or ID already exists"
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (full_name, phone_number, email, address, id_number, password_hash)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (full_name, phone_number, email, address, id_number, password_hash))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"User registered successfully with ID: {user_id}"
            
        except Exception as e:
            return False, f"Registration failed: {str(e)}"
    
    def authenticate_user(self, phone_number, password):
        """Authenticate user login"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT id, full_name, phone_number, email, user_type 
                FROM users 
                WHERE phone_number = ? AND password_hash = ? AND is_active = 1
            ''', (phone_number, password_hash))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                return True, {
                    'id': user[0],
                    'full_name': user[1],
                    'phone_number': user[2],
                    'email': user[3],
                    'user_type': user[4]
                }
            else:
                return False, "Invalid credentials"
                
        except Exception as e:
            return False, f"Authentication failed: {str(e)}"
    
    def install_water_meter(self, user_id, meter_number, location):
        """Install water meter for user"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if meter number already exists
            cursor.execute('SELECT id FROM water_meters WHERE meter_number = ?', (meter_number,))
            if cursor.fetchone():
                return False, "Meter number already exists"
            
            cursor.execute('''
                INSERT INTO water_meters (meter_number, user_id, location, installation_date)
                VALUES (?, ?, ?, ?)
            ''', (meter_number, user_id, location, datetime.date.today()))
            
            meter_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"Water meter installed successfully with ID: {meter_id}"
            
        except Exception as e:
            return False, f"Meter installation failed: {str(e)}"

    def record_water_reading(self, meter_id, reading_value, reading_type='automatic'):
        """Record water meter reading"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get previous reading
            cursor.execute('''
                SELECT reading_value FROM water_readings
                WHERE meter_id = ?
                ORDER BY reading_date DESC LIMIT 1
            ''', (meter_id,))

            previous_reading = cursor.fetchone()
            previous_value = previous_reading[0] if previous_reading else 0

            # Calculate consumption
            consumption = max(0, reading_value - previous_value)
            cost = consumption * self.water_rate_per_cubic_meter

            cursor.execute('''
                INSERT INTO water_readings (meter_id, reading_value, reading_type, consumption, cost)
                VALUES (?, ?, ?, ?, ?)
            ''', (meter_id, reading_value, reading_type, consumption, cost))

            # Update meter's last reading
            cursor.execute('''
                UPDATE water_meters SET last_reading = ? WHERE id = ?
            ''', (reading_value, meter_id))

            conn.commit()
            conn.close()

            return True, f"Reading recorded successfully. Consumption: {consumption} m³, Cost: {cost} RWF"

        except Exception as e:
            return False, f"Failed to record reading: {str(e)}"

# Initialize the system
water_system = SmartWaterSystem()

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        full_name = data.get('full_name')
        phone_number = data.get('phone_number')
        email = data.get('email', '')
        address = data.get('address')
        id_number = data.get('id_number')
        password = data.get('password')

        if not all([full_name, phone_number, address, id_number, password]):
            return jsonify({'success': False, 'message': 'All required fields must be filled'}), 400

        success, message = water_system.register_user(full_name, phone_number, email, address, id_number, password)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/login', methods=['POST'])
def login_user():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        password = data.get('password')

        if not all([phone_number, password]):
            return jsonify({'success': False, 'message': 'Phone number and password are required'}), 400

        success, result = water_system.authenticate_user(phone_number, password)

        if success:
            return jsonify({'success': True, 'user': result})
        else:
            return jsonify({'success': False, 'message': result}), 401

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/meter/install', methods=['POST'])
def install_meter():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        meter_number = data.get('meter_number')
        location = data.get('location')

        if not all([user_id, meter_number, location]):
            return jsonify({'success': False, 'message': 'All fields are required'}), 400

        success, message = water_system.install_water_meter(user_id, meter_number, location)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/reading/record', methods=['POST'])
def record_reading():
    try:
        data = request.get_json()
        meter_id = data.get('meter_id')
        reading_value = data.get('reading_value')
        reading_type = data.get('reading_type', 'manual')

        if not all([meter_id, reading_value]):
            return jsonify({'success': False, 'message': 'Meter ID and reading value are required'}), 400

        success, message = water_system.record_water_reading(meter_id, float(reading_value), reading_type)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5001)
