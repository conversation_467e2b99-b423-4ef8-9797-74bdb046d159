// Mobile Loan Scoring App JavaScript

// Global variables
let currentUser = null;
let currentLanguage = 'en';

// Language translations
const translations = {
  en: {
    // Navigation
    home: "Home",
    login: "Login",
    register: "Register",
    admin: "Admin",
    
    // System
    app_title: "Mobile Loan App",
    system_title: "Mobile Loan Scoring App",
    hero_subtitle: "Smart Loans for Informal Workers in Rwanda",
    hero_description: "Get instant loans based on your mobile money history, airtime purchases, and savings behavior. No collateral required - your digital footprint is your credit score.",
    
    // Hero Stats
    instant_approval: "Instant Approval",
    under_5_minutes: "Under 5 Minutes",
    no_collateral: "No Collateral",
    mobile_data_only: "Mobile Data Only",
    smart_scoring: "Smart Scoring",
    ai_powered: "AI Powered",
    flexible_terms: "Flexible Terms",
    up_to_500k: "Up to 500K RWF",
    
    // Action Cards
    check_score: "Check Your Credit Score",
    check_score_desc: "See your credit score based on mobile money activity",
    apply_loan: "Apply for Loan",
    apply_loan_desc: "Get instant loan approval up to 500,000 RWF",
    join_now: "Join Now",
    join_now_desc: "Register and start building your credit score",
    free: "Free",
    free_registration: "Free Registration",
    
    // How It Works
    how_it_works: "How It Works",
    sync_data: "1. Sync Mobile Data",
    sync_data_desc: "Connect your MTN/Airtel mobile money account",
    get_score: "2. Get Credit Score",
    get_score_desc: "AI analyzes your transaction patterns",
    apply_instantly: "3. Apply Instantly",
    apply_instantly_desc: "Get approved in under 5 minutes",
    receive_funds: "4. Receive Funds",
    receive_funds_desc: "Money sent directly to your mobile wallet",
    
    // Loan Calculator
    loan_calculator: "Loan Calculator",
    loan_amount: "Loan Amount (RWF)",
    loan_term: "Loan Term (Days)",
    interest_rate: "Interest Rate",
    total_repayment: "Total Repayment",
    daily_payment: "Daily Payment",
    
    // Forms
    user_login: "User Login",
    user_login_desc: "Access your loan dashboard",
    phone_number: "Phone Number",
    enter_phone: "Enter your phone number",
    password: "Password",
    enter_password: "Enter your password",
    back: "Back",
    dont_have_account: "Don't have an account?",
    register_here: "Register here",
    
    user_registration: "User Registration",
    user_registration_desc: "Create your loan account",
    full_name: "Full Name",
    enter_full_name: "Enter your full name",
    national_id: "National ID",
    enter_national_id: "Enter your National ID",
    enter_phone_number: "Enter your phone number",
    date_of_birth: "Date of Birth",
    occupation: "Occupation",
    monthly_income: "Monthly Income (RWF)",
    enter_monthly_income: "Enter estimated monthly income",
    address: "Address",
    enter_address: "Enter your full address",
    create_password: "Create a strong password",
    already_have_account: "Already have an account?",
    login_here: "Login here",
    
    processing: "Processing..."
  },
  fr: {
    // Navigation
    home: "Accueil",
    login: "Connexion",
    register: "S'inscrire",
    admin: "Admin",
    
    // System
    app_title: "App de Prêt Mobile",
    system_title: "App de Scoring de Prêt Mobile",
    hero_subtitle: "Prêts Intelligents pour Travailleurs Informels au Rwanda",
    hero_description: "Obtenez des prêts instantanés basés sur votre historique mobile money, achats de crédit et comportement d'épargne. Aucune garantie requise - votre empreinte numérique est votre score de crédit.",
    
    // Hero Stats
    instant_approval: "Approbation Instantanée",
    under_5_minutes: "Moins de 5 Minutes",
    no_collateral: "Aucune Garantie",
    mobile_data_only: "Données Mobile Seulement",
    smart_scoring: "Scoring Intelligent",
    ai_powered: "Alimenté par IA",
    flexible_terms: "Conditions Flexibles",
    up_to_500k: "Jusqu'à 500K RWF",
    
    // Action Cards
    check_score: "Vérifiez Votre Score de Crédit",
    check_score_desc: "Voir votre score de crédit basé sur l'activité mobile money",
    apply_loan: "Demander un Prêt",
    apply_loan_desc: "Obtenez une approbation de prêt instantanée jusqu'à 500,000 RWF",
    join_now: "Rejoignez Maintenant",
    join_now_desc: "Inscrivez-vous et commencez à construire votre score de crédit",
    free: "Gratuit",
    free_registration: "Inscription Gratuite",
    
    // How It Works
    how_it_works: "Comment Ça Marche",
    sync_data: "1. Synchroniser les Données Mobile",
    sync_data_desc: "Connectez votre compte mobile money MTN/Airtel",
    get_score: "2. Obtenir le Score de Crédit",
    get_score_desc: "L'IA analyse vos modèles de transaction",
    apply_instantly: "3. Postuler Instantanément",
    apply_instantly_desc: "Soyez approuvé en moins de 5 minutes",
    receive_funds: "4. Recevoir les Fonds",
    receive_funds_desc: "Argent envoyé directement à votre portefeuille mobile",
    
    // Loan Calculator
    loan_calculator: "Calculateur de Prêt",
    loan_amount: "Montant du Prêt (RWF)",
    loan_term: "Durée du Prêt (Jours)",
    interest_rate: "Taux d'Intérêt",
    total_repayment: "Remboursement Total",
    daily_payment: "Paiement Quotidien",
    
    // Forms
    user_login: "Connexion Utilisateur",
    user_login_desc: "Accédez à votre tableau de bord de prêt",
    phone_number: "Numéro de Téléphone",
    enter_phone: "Entrez votre numéro de téléphone",
    password: "Mot de Passe",
    enter_password: "Entrez votre mot de passe",
    back: "Retour",
    dont_have_account: "Vous n'avez pas de compte?",
    register_here: "Inscrivez-vous ici",
    
    user_registration: "Inscription Utilisateur",
    user_registration_desc: "Créez votre compte de prêt",
    full_name: "Nom Complet",
    enter_full_name: "Entrez votre nom complet",
    national_id: "Carte d'Identité Nationale",
    enter_national_id: "Entrez votre carte d'identité nationale",
    enter_phone_number: "Entrez votre numéro de téléphone",
    date_of_birth: "Date de Naissance",
    occupation: "Profession",
    monthly_income: "Revenu Mensuel (RWF)",
    enter_monthly_income: "Entrez le revenu mensuel estimé",
    address: "Adresse",
    enter_address: "Entrez votre adresse complète",
    create_password: "Créez un mot de passe fort",
    already_have_account: "Vous avez déjà un compte?",
    login_here: "Connectez-vous ici",
    
    processing: "Traitement en cours..."
  },
  rw: {
    // Navigation
    home: "Ahabanza",
    login: "Injira",
    register: "Iyandikishe",
    admin: "Umuyobozi",
    
    // System
    app_title: "Porogaramu y'Inguzanyo za Telefoni",
    system_title: "Porogaramu y'Inguzanyo za Telefoni",
    hero_subtitle: "Inguzanyo Zubwenge ku Bakozi Batemewe mu Rwanda",
    hero_description: "Bonera inguzanyo ako kanya zishingiye ku mateka yawe ya mobile money, kugura airtime, n'imyitwarire y'ikigega. Nta ngwate ikenewe - ibimenyetso byawe bya digitale ni amanota yawe y'inguzanyo.",
    
    // Hero Stats
    instant_approval: "Kwemererwa ako kanya",
    under_5_minutes: "Munsi y'iminota 5",
    no_collateral: "Nta ngwate",
    mobile_data_only: "Amakuru ya Telefoni gusa",
    smart_scoring: "Amanota Yubwenge",
    ai_powered: "Ikoreshwa na AI",
    flexible_terms: "Ibisabwa Byoroshye",
    up_to_500k: "Kugeza kuri 500K RWF",
    
    // Action Cards
    check_score: "Reba Amanota yawe y'Inguzanyo",
    check_score_desc: "Reba amanota yawe y'inguzanyo ashingiye ku bikorwa bya mobile money",
    apply_loan: "Saba Inguzanyo",
    apply_loan_desc: "Bonera kwemererwa inguzanyo ako kanya kugeza kuri 500,000 RWF",
    join_now: "Jya Ubu",
    join_now_desc: "Iyandikishe utangire kubaka amanota yawe y'inguzanyo",
    free: "Ubuntu",
    free_registration: "Kwiyandikisha Ubuntu",
    
    // How It Works
    how_it_works: "Uburyo Bikora",
    sync_data: "1. Guhuza Amakuru ya Telefoni",
    sync_data_desc: "Huza konti yawe ya mobile money ya MTN/Airtel",
    get_score: "2. Bonera Amanota y'Inguzanyo",
    get_score_desc: "AI isesengura imiterere y'ibikorwa byawe",
    apply_instantly: "3. Saba ako kanya",
    apply_instantly_desc: "Emererwa munsi y'iminota 5",
    receive_funds: "4. Kwakira Amafaranga",
    receive_funds_desc: "Amafaranga yoherezwa mu gikapu cyawe cya mobile",
    
    // Loan Calculator
    loan_calculator: "Kubara Inguzanyo",
    loan_amount: "Ingano y'Inguzanyo (RWF)",
    loan_term: "Igihe cy'Inguzanyo (Iminsi)",
    interest_rate: "Igipimo cy'Inyungu",
    total_repayment: "Kwishyura Kwose",
    daily_payment: "Kwishyura buri munsi",
    
    // Forms
    user_login: "Kwinjira kw'Ukoresha",
    user_login_desc: "Injira mu kibaho cyawe cy'inguzanyo",
    phone_number: "Nimero ya Telefoni",
    enter_phone: "Injiza nimero ya telefoni yawe",
    password: "Ijambo ry'Ibanga",
    enter_password: "Injiza ijambo ryawe ry'ibanga",
    back: "Subira",
    dont_have_account: "Ntufite konti?",
    register_here: "Iyandikishe hano",
    
    user_registration: "Kwiyandikisha kw'Ukoresha",
    user_registration_desc: "Kora konti yawe y'inguzanyo",
    full_name: "Amazina Yuzuye",
    enter_full_name: "Injiza amazina yawe yuzuye",
    national_id: "Indangamuntu",
    enter_national_id: "Injiza indangamuntu yawe",
    enter_phone_number: "Injiza nimero ya telefoni yawe",
    date_of_birth: "Italiki y'Amavuko",
    occupation: "Akazi",
    monthly_income: "Amafaranga y'Ukwezi (RWF)",
    enter_monthly_income: "Injiza amafaranga y'ukwezi ateganijwe",
    address: "Aderesi",
    enter_address: "Injiza aderesi yawe yuzuye",
    create_password: "Kora ijambo ry'ibanga rikomeye",
    already_have_account: "Usanzwe ufite konti?",
    login_here: "Injira hano",
    
    processing: "Gutegura..."
  }
};

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  // Check for saved user session
  const savedUser = localStorage.getItem("mobileLoanUser");
  if (savedUser) {
    currentUser = JSON.parse(savedUser);
    updateNavigation();
  }

  // Add event listeners
  const loginForm = document.getElementById("login-form");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  const registerForm = document.getElementById("register-form");
  if (registerForm) {
    registerForm.addEventListener("submit", handleRegister);
  }

  // Load saved language
  const savedLanguage = localStorage.getItem("mobileLoanLanguage");
  if (savedLanguage) {
    currentLanguage = savedLanguage;
    updateLanguage();
    updateLanguageButtons();
  }

  // Initialize loan calculator
  updateCalculator();

  // Initialize PWA
  initializePWA();
});

// Screen navigation functions
function showScreen(screenId) {
  const screens = document.querySelectorAll(".screen");
  screens.forEach((screen) => screen.classList.remove("active"));

  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add("active");
  }
}

function showHome() {
  showScreen("home-screen");
}

function showLogin() {
  showScreen("login-screen");
}

function showRegister() {
  showScreen("register-screen");
}

function showDashboard() {
  showScreen("dashboard-screen");
  if (currentUser) {
    loadDashboardData();
  }
}

function showAdminLogin() {
  showToast("Admin features coming soon!", "info");
}

function updateNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  
  if (currentUser) {
    // Update navigation for logged-in users
    navLinks.forEach(link => {
      if (link.textContent.includes("Login")) {
        link.innerHTML = '<i class="fas fa-tachometer-alt"></i> Dashboard';
        link.onclick = () => showDashboard();
      }
    });
  }
}

// Loan calculator functions
function updateCalculator() {
  const loanAmount = document.getElementById("loanAmount");
  const loanTerm = document.getElementById("loanTerm");
  const loanAmountDisplay = document.getElementById("loanAmountDisplay");
  const loanTermDisplay = document.getElementById("loanTermDisplay");
  const interestRate = document.getElementById("interestRate");
  const totalRepayment = document.getElementById("totalRepayment");
  const dailyPayment = document.getElementById("dailyPayment");

  if (!loanAmount || !loanTerm) return;

  const amount = parseInt(loanAmount.value);
  const term = parseInt(loanTerm.value);
  const rate = 0.15; // 15% interest rate

  const interest = amount * rate;
  const total = amount + interest;
  const daily = total / term;

  if (loanAmountDisplay) loanAmountDisplay.textContent = `${amount.toLocaleString()} RWF`;
  if (loanTermDisplay) loanTermDisplay.textContent = `${term} days`;
  if (interestRate) interestRate.textContent = `${(rate * 100)}%`;
  if (totalRepayment) totalRepayment.textContent = `${total.toLocaleString()} RWF`;
  if (dailyPayment) dailyPayment.textContent = `${Math.round(daily).toLocaleString()} RWF`;
}

// Authentication functions
async function handleLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    phone_number: formData.get("phone_number"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.user;
      localStorage.setItem("mobileLoanUser", JSON.stringify(currentUser));
      showToast("Login successful!", "success");
      updateNavigation();
      showDashboard();
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Login failed. Please try again.", "error");
  }
}

async function handleRegister(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const registerData = {
    full_name: formData.get("full_name"),
    phone_number: formData.get("phone_number"),
    national_id: formData.get("national_id"),
    date_of_birth: formData.get("date_of_birth"),
    address: formData.get("address"),
    occupation: formData.get("occupation"),
    monthly_income: formData.get("monthly_income"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/register", "POST", registerData);
    hideLoading();

    if (result.success) {
      showToast("Registration successful! Please login.", "success");
      showLogin();
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Registration failed. Please try again.", "error");
  }
}

function logout() {
  currentUser = null;
  localStorage.removeItem("mobileLoanUser");
  showToast("Logged out successfully!", "success");
  showHome();
  updateNavigation();
}

// Language functions
function changeLanguage(lang) {
  currentLanguage = lang;
  localStorage.setItem("mobileLoanLanguage", lang);
  updateLanguage();
  updateLanguageButtons();
  showToast(`Language changed to ${lang.toUpperCase()}`, "success");
}

function updateLanguage() {
  // Update text content
  const elements = document.querySelectorAll("[data-translate]");
  elements.forEach((element) => {
    const key = element.getAttribute("data-translate");
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      element.textContent = translations[currentLanguage][key];
    }
  });
  
  // Update placeholders
  const placeholderElements = document.querySelectorAll(
    "[data-translate-placeholder]"
  );
  placeholderElements.forEach((element) => {
    const key = element.getAttribute("data-translate-placeholder");
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      element.placeholder = translations[currentLanguage][key];
    }
  });
}

function updateLanguageButtons() {
  const languageButtons = document.querySelectorAll(".language-btn");
  languageButtons.forEach(btn => {
    btn.classList.remove("active");
    if (btn.textContent.includes(currentLanguage.toUpperCase())) {
      btn.classList.add("active");
    }
  });
}

// Utility functions
function showLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.add("active");
  }
}

function hideLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.remove("active");
  }
}

function showToast(message, type = "info") {
  const toastContainer = document.getElementById("toast-container");
  if (!toastContainer) return;

  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.textContent = message;

  toastContainer.appendChild(toast);

  // Remove toast after 5 seconds
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 5000);
}

// API helper function
async function apiCall(endpoint, method = "GET", data = null) {
  const url = `/api${endpoint}`;
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data && method !== "GET") {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  return await response.json();
}

// PWA functions
function initializePWA() {
  // Register service worker
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("SW registered: ", registration);
        })
        .catch((registrationError) => {
          console.log("SW registration failed: ", registrationError);
        });
    });
  }
}
