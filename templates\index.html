<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Curfew E-Pass System</title>

    <!-- PWA Meta Tags -->
    <meta
      name="description"
      content="Secure and efficient digital pass management system with smart duplicate detection"
    />
    <meta name="theme-color" content="#4f46e5" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="E-Pass System" />
    <meta name="msapplication-TileColor" content="#4f46e5" />
    <meta name="msapplication-tap-highlight" content="no" />

    <!-- PWA Manifest -->
    <link
      rel="manifest"
      href="{{ url_for('static', filename='manifest.json') }}"
    />

    <!-- PWA Icons -->
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="{{ url_for('static', filename='icons/icon-32x32.png') }}"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="{{ url_for('static', filename='icons/icon-16x16.png') }}"
    />
    <link
      rel="apple-touch-icon"
      href="{{ url_for('static', filename='icons/icon-192x192.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="{{ url_for('static', filename='icons/icon-152x152.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="{{ url_for('static', filename='icons/icon-192x192.png') }}"
    />
    <link
      rel="apple-touch-icon"
      sizes="167x167"
      href="{{ url_for('static', filename='icons/icon-192x192.png') }}"
    />

    <!-- Stylesheets -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/pwa.css') }}"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <i class="fas fa-shield-alt"></i>
          <span>E-Pass System</span>
        </div>
        <div class="nav-links">
          <a href="#" onclick="showHome()" class="nav-link active">Home</a>
          <a href="#" onclick="showRequestForm()" class="nav-link"
            >Request E-Pass</a
          >
          <a href="#" onclick="showStatusCheck()" class="nav-link"
            >Check Status</a
          >
          <a href="#" onclick="showAdminLogin()" class="nav-link">Admin</a>
        </div>
      </nav>

      <!-- Home Screen -->
      <div id="home-screen" class="screen active">
        <div class="hero-section">
          <div class="hero-content">
            <h1><i class="fas fa-shield-alt"></i> Curfew E-Pass System</h1>
            <p class="hero-subtitle">
              Secure and efficient digital pass management system
            </p>
            <div class="hero-stats">
              <div class="stat-card">
                <i class="fas fa-clock"></i>
                <h3>24/7</h3>
                <p>Available</p>
              </div>
              <div class="stat-card">
                <i class="fas fa-shield-check"></i>
                <h3>Smart</h3>
                <p>Duplicate Detection</p>
              </div>
              <div class="stat-card">
                <i class="fas fa-mobile-alt"></i>
                <h3>Mobile</h3>
                <p>Friendly</p>
              </div>
            </div>
          </div>
        </div>

        <div class="action-cards">
          <div class="card" onclick="showRequestForm()">
            <div class="card-icon">
              <i class="fas fa-plus-circle"></i>
            </div>
            <h3>Request E-Pass</h3>
            <p>Submit a new e-pass request with required documents</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <div class="card" onclick="showStatusCheck()">
            <div class="card-icon">
              <i class="fas fa-search"></i>
            </div>
            <h3>Check Status</h3>
            <p>Track your e-pass request status using request ID</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <div class="card" onclick="showAdminLogin()">
            <div class="card-icon">
              <i class="fas fa-user-shield"></i>
            </div>
            <h3>Admin Panel</h3>
            <p>Administrative access for request management</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Request Form Screen -->
      <div id="request-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-plus-circle"></i> Request E-Pass</h2>
            <p>
              Fill in all required information to submit your e-pass request
            </p>
          </div>

          <form id="request-form" class="form">
            <div class="form-group">
              <label for="name">
                <i class="fas fa-user"></i>
                Full Name *
              </label>
              <input type="text" id="name" name="name" required />
            </div>

            <div class="form-group">
              <label for="contact">
                <i class="fas fa-phone"></i>
                Contact Number *
              </label>
              <input type="tel" id="contact" name="contact" required />
            </div>

            <div class="form-group">
              <label for="id_proof">
                <i class="fas fa-id-card"></i>
                ID Proof Number *
              </label>
              <input type="text" id="id_proof" name="id_proof" required />
            </div>

            <div class="form-group">
              <label for="reason">
                <i class="fas fa-clipboard-list"></i>
                Reason for E-Pass *
              </label>
              <textarea id="reason" name="reason" rows="4" required></textarea>
            </div>

            <div class="form-group">
              <label for="attachment">
                <i class="fas fa-paperclip"></i>
                Attachment (Optional)
              </label>
              <div class="file-input-wrapper">
                <input
                  type="file"
                  id="attachment"
                  name="attachment"
                  accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.txt"
                />
                <span class="file-input-text">No file selected</span>
              </div>
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn btn-secondary"
                onclick="showHome()"
              >
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-paper-plane"></i> Submit Request
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Status Check Screen -->
      <div id="status-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-search"></i> Check Request Status</h2>
            <p>Enter your request ID to check the current status</p>
          </div>

          <form id="status-form" class="form">
            <div class="form-group">
              <label for="request_id">
                <i class="fas fa-hashtag"></i>
                Request ID *
              </label>
              <input
                type="number"
                id="request_id"
                name="request_id"
                required
                min="1"
              />
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn btn-secondary"
                onclick="showHome()"
              >
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Check Status
              </button>
            </div>
          </form>

          <div id="status-result" class="status-result" style="display: none">
            <!-- Status result will be displayed here -->
          </div>
        </div>
      </div>

      <!-- Admin Login Screen -->
      <div id="admin-login-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-user-shield"></i> Admin Login</h2>
            <p>Enter your administrative credentials</p>
          </div>

          <form id="admin-login-form" class="form">
            <div class="form-group">
              <label for="admin_username">
                <i class="fas fa-user"></i>
                Username *
              </label>
              <input type="text" id="admin_username" name="username" required />
            </div>

            <div class="form-group">
              <label for="admin_password">
                <i class="fas fa-lock"></i>
                Password *
              </label>
              <input
                type="password"
                id="admin_password"
                name="password"
                required
              />
            </div>

            <div class="form-actions">
              <button
                type="button"
                class="btn btn-secondary"
                onclick="showHome()"
              >
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Admin Panel Screen -->
      <div id="admin-panel-screen" class="screen">
        <div class="admin-container">
          <div class="admin-header">
            <div class="admin-title-section">
              <h2><i class="fas fa-cogs"></i> Admin Panel</h2>
              <button
                class="btn btn-secondary logout-btn"
                onclick="logoutAdmin()"
              >
                <i class="fas fa-sign-out-alt"></i> Logout
              </button>
            </div>
            <div class="admin-stats" id="admin-stats">
              <!-- Stats will be loaded here -->
            </div>
          </div>

          <div class="admin-controls">
            <div class="control-group">
              <label for="admin_request_id">
                <i class="fas fa-hashtag"></i>
                Request ID to Process:
              </label>
              <input type="number" id="admin_request_id" min="1" />
              <div class="control-buttons">
                <button class="btn btn-success" onclick="approveRequest()">
                  <i class="fas fa-check"></i> Approve
                </button>
                <button class="btn btn-warning" onclick="denyRequest()">
                  <i class="fas fa-times"></i> Deny
                </button>
                <button class="btn btn-danger" onclick="deleteRequest()">
                  <i class="fas fa-trash"></i> Delete
                </button>
              </div>
            </div>

            <div class="danger-zone">
              <button class="btn btn-danger" onclick="deleteAllRequests()">
                <i class="fas fa-trash-alt"></i> Delete All Requests
              </button>
              <button class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back to Home
              </button>
            </div>
          </div>

          <div class="requests-container">
            <h3><i class="fas fa-list"></i> All Requests</h3>
            <div id="requests-list" class="requests-list">
              <!-- Requests will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="loading-overlay">
      <div class="loading-spinner">
        <i class="fas fa-spinner fa-spin"></i>
        <p>Processing...</p>
      </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pwa.js') }}"></script>
  </body>
</html>
