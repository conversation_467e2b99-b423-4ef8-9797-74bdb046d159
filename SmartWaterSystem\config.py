# Smart Water System Configuration

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'smart_water_system_2024_secret_key'
    
    # Database Configuration
    DATABASE_NAME = 'smart_water.db'
    DATABASE_URL = os.environ.get('DATABASE_URL') or f'sqlite:///{DATABASE_NAME}'
    
    # Water Billing Configuration
    WATER_RATE_PER_CUBIC_METER = 500  # RWF per cubic meter
    MONTHLY_SERVICE_FEE = 1000  # RWF monthly service fee
    METER_INSTALLATION_FEE = 25000  # RWF one-time installation fee
    
    # Leak Detection Configuration
    LEAK_DETECTION_THRESHOLD = 50  # Cubic meters per day threshold
    LEAK_ALERT_SEVERITY_LEVELS = ['low', 'medium', 'high', 'critical']
    
    # Payment Configuration
    SUPPORTED_PAYMENT_METHODS = [
        'mtn_mobile_money',
        'airtel_money',
        'bank_transfer',
        'cash',
        'card'
    ]
    
    # Mobile Money API Configuration (Rwanda)
    MTN_MOMO_API_URL = 'https://sandbox.momodeveloper.mtn.com'
    MTN_MOMO_API_KEY = os.environ.get('MTN_MOMO_API_KEY')
    MTN_MOMO_USER_ID = os.environ.get('MTN_MOMO_USER_ID')
    
    AIRTEL_MONEY_API_URL = 'https://openapiuat.airtel.africa'
    AIRTEL_MONEY_CLIENT_ID = os.environ.get('AIRTEL_MONEY_CLIENT_ID')
    AIRTEL_MONEY_CLIENT_SECRET = os.environ.get('AIRTEL_MONEY_CLIENT_SECRET')
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # File Upload Configuration
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
    
    # Email Configuration (for notifications)
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'smtp.gmail.com'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # SMS Configuration (for alerts)
    SMS_API_URL = os.environ.get('SMS_API_URL')
    SMS_API_KEY = os.environ.get('SMS_API_KEY')
    SMS_SENDER_ID = os.environ.get('SMS_SENDER_ID') or 'SmartWater'
    
    # System Configuration
    SYSTEM_NAME = 'Smart Water Distribution & Billing System'
    SYSTEM_VERSION = '1.0.0'
    COMPANY_NAME = 'Smart Water Solutions Rwanda'
    SUPPORT_EMAIL = '<EMAIL>'
    SUPPORT_PHONE = '+250 XXX XXX XXX'
    
    # Timezone Configuration
    TIMEZONE = 'Africa/Kigali'
    
    # Language Configuration
    SUPPORTED_LANGUAGES = ['en', 'rw', 'fr']  # English, Kinyarwanda, French
    DEFAULT_LANGUAGE = 'en'
    
    # Rate Limiting Configuration
    RATELIMIT_STORAGE_URL = 'memory://'
    RATELIMIT_DEFAULT = '100 per hour'
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = 'smart_water_system.log'
    
    # IoT Configuration
    IOT_MQTT_BROKER = os.environ.get('IOT_MQTT_BROKER') or 'localhost'
    IOT_MQTT_PORT = int(os.environ.get('IOT_MQTT_PORT') or 1883)
    IOT_MQTT_USERNAME = os.environ.get('IOT_MQTT_USERNAME')
    IOT_MQTT_PASSWORD = os.environ.get('IOT_MQTT_PASSWORD')
    
    # Analytics Configuration
    ANALYTICS_RETENTION_DAYS = 365  # Keep analytics data for 1 year
    REPORT_GENERATION_SCHEDULE = '0 6 1 * *'  # Monthly reports at 6 AM on 1st day
    
    # Backup Configuration
    BACKUP_SCHEDULE = '0 2 * * *'  # Daily backup at 2 AM
    BACKUP_RETENTION_DAYS = 30
    BACKUP_LOCATION = 'backups/'

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Development database
    DATABASE_NAME = 'smart_water_dev.db'
    
    # Relaxed security for development
    SESSION_COOKIE_SECURE = False
    
    # Development logging
    LOG_LEVEL = 'DEBUG'

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production database
    DATABASE_NAME = 'smart_water_prod.db'
    
    # Enhanced security for production
    SESSION_COOKIE_SECURE = True
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    # Production rate limiting
    RATELIMIT_DEFAULT = '50 per hour'

class TestingConfig(Config):
    """Testing configuration"""
    DEBUG = True
    TESTING = True
    
    # Test database
    DATABASE_NAME = 'smart_water_test.db'
    
    # Disable CSRF for testing
    WTF_CSRF_ENABLED = False

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Water tariff structure for Rwanda
WATER_TARIFF_STRUCTURE = {
    'domestic': {
        '0-5': 200,      # First 5 m³ at 200 RWF/m³
        '6-15': 350,     # Next 10 m³ at 350 RWF/m³
        '16-30': 500,    # Next 15 m³ at 500 RWF/m³
        '31+': 750       # Above 30 m³ at 750 RWF/m³
    },
    'commercial': {
        '0-10': 600,     # First 10 m³ at 600 RWF/m³
        '11+': 800       # Above 10 m³ at 800 RWF/m³
    },
    'industrial': {
        'all': 1000      # Flat rate of 1000 RWF/m³
    }
}

# System constants
SYSTEM_CONSTANTS = {
    'MIN_PASSWORD_LENGTH': 8,
    'MAX_LOGIN_ATTEMPTS': 5,
    'ACCOUNT_LOCKOUT_DURATION': 30,  # minutes
    'PASSWORD_RESET_TOKEN_EXPIRY': 24,  # hours
    'BILL_DUE_DAYS': 30,
    'LATE_PAYMENT_PENALTY': 0.05,  # 5% penalty
    'RECONNECTION_FEE': 5000,  # RWF
    'METER_READING_FREQUENCY': 'daily',  # daily, weekly, monthly
    'LEAK_DETECTION_SENSITIVITY': 'medium',  # low, medium, high
    'NOTIFICATION_PREFERENCES': ['email', 'sms', 'push'],
    'CURRENCY': 'RWF',
    'CURRENCY_SYMBOL': 'RWF',
    'DATE_FORMAT': '%Y-%m-%d',
    'DATETIME_FORMAT': '%Y-%m-%d %H:%M:%S',
    'DECIMAL_PLACES': 2
}
