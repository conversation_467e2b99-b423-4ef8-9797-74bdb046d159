// Global variables
let isAdminLoggedIn = false;
let allRequests = [];

// Utility functions
function showLoading() {
  document.getElementById("loading-overlay").style.display = "flex";
}

function hideLoading() {
  document.getElementById("loading-overlay").style.display = "none";
}

function showToast(message, type = "info") {
  const toastContainer = document.getElementById("toast-container");
  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-${
              type === "success"
                ? "check-circle"
                : type === "error"
                ? "exclamation-circle"
                : "info-circle"
            }"></i>
            <span>${message}</span>
        </div>
    `;

  toastContainer.appendChild(toast);

  // Auto remove after 5 seconds
  setTimeout(() => {
    toast.remove();
  }, 5000);
}

// Navigation functions
function showScreen(screenId) {
  // Hide all screens
  document.querySelectorAll(".screen").forEach((screen) => {
    screen.classList.remove("active");
  });

  // Show target screen
  document.getElementById(screenId).classList.add("active");

  // Update navigation
  document.querySelectorAll(".nav-link").forEach((link) => {
    link.classList.remove("active");
  });
}

function showHome() {
  showScreen("home-screen");
  document
    .querySelector('.nav-link[onclick="showHome()"]')
    .classList.add("active");
}

function showRequestForm() {
  showScreen("request-screen");
  document
    .querySelector('.nav-link[onclick="showRequestForm()"]')
    .classList.add("active");
  document.getElementById("request-form").reset();
  updateFileInputText();
}

function showStatusCheck() {
  showScreen("status-screen");
  document
    .querySelector('.nav-link[onclick="showStatusCheck()"]')
    .classList.add("active");
  document.getElementById("status-form").reset();
  document.getElementById("status-result").style.display = "none";
}

function showAdminLogin() {
  // Always show login screen, even if previously logged in
  showScreen("admin-login-screen");
  document
    .querySelector('.nav-link[onclick="showAdminLogin()"]')
    .classList.add("active");
  document.getElementById("admin-login-form").reset();

  // Update the login form title based on current state
  const formHeader = document.querySelector(
    "#admin-login-screen .form-header h2"
  );
  if (isAdminLoggedIn) {
    formHeader.innerHTML =
      '<i class="fas fa-user-shield"></i> Re-authenticate Admin Access';
    document.querySelector("#admin-login-screen .form-header p").textContent =
      "Please re-enter your credentials to access the admin panel";
  } else {
    formHeader.innerHTML = '<i class="fas fa-user-shield"></i> Admin Login';
    document.querySelector("#admin-login-screen .form-header p").textContent =
      "Enter your administrative credentials";
  }
}

function showAdminPanel() {
  showScreen("admin-panel-screen");
  document
    .querySelector('.nav-link[onclick="showAdminLogin()"]')
    .classList.add("active");
  loadAdminData();
}

function logoutAdmin() {
  if (confirm("Are you sure you want to logout from admin panel?")) {
    isAdminLoggedIn = false;
    updateAdminNavText();
    showToast("Logged out successfully!", "success");
    showHome();
  }
}

function updateAdminNavText() {
  const adminNavLink = document.querySelector(
    '.nav-link[onclick="showAdminLogin()"]'
  );
  if (isAdminLoggedIn) {
    adminNavLink.textContent = "Admin Panel";
  } else {
    adminNavLink.textContent = "Admin";
  }
}

// File input handling
function updateFileInputText() {
  const fileInput = document.getElementById("attachment");
  const fileText = document.querySelector(".file-input-text");

  fileInput.addEventListener("change", function () {
    if (this.files && this.files.length > 0) {
      fileText.textContent = this.files[0].name;
    } else {
      fileText.textContent = "No file selected";
    }
  });
}

// API functions
async function apiCall(url, method = "GET", data = null) {
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return result;
  } catch (error) {
    console.error("API Error:", error);
    throw error;
  }
}

// Form handlers
async function handleRequestSubmit(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);

  try {
    // Use FormData directly for file upload support
    const response = await fetch("/api/submit-request", {
      method: "POST",
      body: formData,
    });

    const result = await response.json();
    hideLoading();

    if (result.success) {
      showToast(result.message, "success");
      setTimeout(() => showHome(), 2000);
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Error submitting request. Please try again.", "error");
  }
}

async function handleStatusCheck(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const requestId = formData.get("request_id");

  try {
    const result = await apiCall(`/api/check-status/${requestId}`);
    hideLoading();

    const statusResult = document.getElementById("status-result");

    if (result.success) {
      const request = result.request;
      const statusClass = `status-${request.status.toLowerCase()}`;

      let statusHtml = `
                <div class="status-result ${statusClass}">
                    <h3><i class="fas fa-info-circle"></i> Request Status</h3>
                    <div class="status-details">
                        <p><strong>Request ID:</strong> ${
                          request.request_id
                        }</p>
                        <p><strong>Name:</strong> ${request.name}</p>
                        <p><strong>Status:</strong> ${request.status}</p>
                        <p><strong>Submitted:</strong> ${new Date(
                          request.timestamp
                        ).toLocaleString()}</p>
            `;

      if (request.status === "Approved" && request.e_pass_id) {
        statusHtml += `<p><strong>E-Pass ID:</strong> ${request.e_pass_id}</p>`;
      }

      statusHtml += `
                    </div>
                </div>
            `;

      statusResult.innerHTML = statusHtml;
      statusResult.style.display = "block";
    } else {
      statusResult.innerHTML = `
                <div class="status-result">
                    <h3><i class="fas fa-exclamation-circle"></i> Not Found</h3>
                    <p>${result.message}</p>
                </div>
            `;
      statusResult.style.display = "block";
    }
  } catch (error) {
    hideLoading();
    showToast("Error checking status. Please try again.", "error");
  }
}

async function handleAdminLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const data = {
    username: formData.get("username"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/api/admin-login", "POST", data);
    hideLoading();

    if (result.success) {
      isAdminLoggedIn = true;
      updateAdminNavText();
      showToast("Login successful!", "success");
      setTimeout(() => showAdminPanel(), 1000);
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Login error. Please try again.", "error");
  }
}

// Admin functions
async function loadAdminData() {
  showLoading();

  try {
    const result = await apiCall("/api/admin/requests");
    hideLoading();

    if (result.success) {
      allRequests = result.requests; // Store requests globally
      displayAdminStats(result.stats);
      displayRequests(result.requests);
    } else {
      showToast("Error loading admin data.", "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Error loading admin data.", "error");
  }
}

function displayAdminStats(stats) {
  const statsContainer = document.getElementById("admin-stats");
  statsContainer.innerHTML = `
        <div class="admin-stat">
            <h4>${stats.total_requests}</h4>
            <p>Total Requests</p>
        </div>
        <div class="admin-stat">
            <h4>${stats.approved_requests}</h4>
            <p>Approved Requests</p>
        </div>
        <div class="admin-stat">
            <h4>${stats.denied_requests}</h4>
            <p>Denied Requests</p>
        </div>
    `;
}

function displayRequests(requests) {
  const requestsList = document.getElementById("requests-list");

  if (requests.length === 0) {
    requestsList.innerHTML = "<p>No requests found.</p>";
    return;
  }

  requestsList.innerHTML = requests
    .map(
      (request) => `
        <div class="request-item ${request.status.toLowerCase()}">
            <div class="request-header">
                <span>ID: ${request.request_id} - ${request.name}</span>
                <div class="request-actions">
                    <span class="status-badge">${request.status}</span>
                    <button class="btn btn-info btn-sm" onclick="viewRequestDetails(${
                      request.request_id
                    })">
                        <i class="fas fa-eye"></i> View Details
                    </button>
                </div>
            </div>
            <div class="request-summary">
                <p><strong>Contact:</strong> ${request.contact}</p>
                <p><strong>Date:</strong> ${new Date(
                  request.timestamp
                ).toLocaleString()}</p>
                ${
                  request.e_pass_id
                    ? `<p><strong>E-Pass ID:</strong> ${request.e_pass_id}</p>`
                    : ""
                }
            </div>
        </div>
    `
    )
    .join("");
}

function viewRequestDetails(requestId) {
  const request = allRequests.find((req) => req.request_id === requestId);
  if (!request) {
    showToast("Request not found!", "error");
    return;
  }

  // Create modal content
  const modalContent = `
    <div class="modal-overlay" onclick="closeModal()">
      <div class="modal-content" onclick="event.stopPropagation()">
        <div class="modal-header">
          <h3><i class="fas fa-file-alt"></i> Request Details - ID: ${
            request.request_id
          }</h3>
          <button class="modal-close" onclick="closeModal()">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="detail-grid">
            <div class="detail-item">
              <label><i class="fas fa-hashtag"></i> Request ID:</label>
              <span>${request.request_id}</span>
            </div>
            <div class="detail-item">
              <label><i class="fas fa-user"></i> Full Name:</label>
              <span>${request.name}</span>
            </div>
            <div class="detail-item">
              <label><i class="fas fa-phone"></i> Contact:</label>
              <span>${request.contact}</span>
            </div>
            <div class="detail-item">
              <label><i class="fas fa-id-card"></i> ID Proof:</label>
              <span>${request.id_proof}</span>
            </div>
            <div class="detail-item">
              <label><i class="fas fa-calendar"></i> Submitted:</label>
              <span>${new Date(request.timestamp).toLocaleString()}</span>
            </div>
            <div class="detail-item">
              <label><i class="fas fa-info-circle"></i> Status:</label>
              <span class="status-badge ${request.status.toLowerCase()}">${
    request.status
  }</span>
            </div>
            ${
              request.e_pass_id
                ? `
            <div class="detail-item">
              <label><i class="fas fa-id-badge"></i> E-Pass ID:</label>
              <span>${request.e_pass_id}</span>
            </div>
            `
                : ""
            }
            <div class="detail-item full-width">
              <label><i class="fas fa-clipboard-list"></i> Reason:</label>
              <div class="reason-text">${request.reason}</div>
            </div>
            <div class="detail-item full-width">
              <label><i class="fas fa-paperclip"></i> Attachment:</label>
              <div class="attachment-section">
                ${
                  request.attachment &&
                  request.attachment !== "No file selected."
                    ? `
                  <span class="attachment-name">${request.attachment}</span>
                  <button class="btn btn-sm btn-primary" onclick="openAttachment('${request.attachment}')">
                    <i class="fas fa-external-link-alt"></i> Open Attachment
                  </button>
                `
                    : '<span class="no-attachment">No attachment provided</span>'
                }
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeModal()">
            <i class="fas fa-times"></i> Close
          </button>
        </div>
      </div>
    </div>
  `;

  // Add modal to page
  document.body.insertAdjacentHTML("beforeend", modalContent);
}

function closeModal() {
  const modal = document.querySelector(".modal-overlay");
  if (modal) {
    modal.remove();
  }
}

function openAttachment(filename) {
  // Create a temporary link to download/open the attachment
  window.open(`/uploads/${filename}`, "_blank");
}

async function approveRequest() {
  const requestId = document.getElementById("admin_request_id").value;
  if (!requestId) {
    showToast("Please enter a request ID.", "warning");
    return;
  }

  showLoading();

  try {
    const result = await apiCall(`/api/admin/approve/${requestId}`, "POST");
    hideLoading();

    showToast(result.message, result.success ? "success" : "error");
    if (result.success) {
      loadAdminData();
      document.getElementById("admin_request_id").value = "";
    }
  } catch (error) {
    hideLoading();
    showToast("Error approving request.", "error");
  }
}

async function denyRequest() {
  const requestId = document.getElementById("admin_request_id").value;
  if (!requestId) {
    showToast("Please enter a request ID.", "warning");
    return;
  }

  showLoading();

  try {
    const result = await apiCall(`/api/admin/deny/${requestId}`, "POST");
    hideLoading();

    showToast(result.message, result.success ? "success" : "error");
    if (result.success) {
      loadAdminData();
      document.getElementById("admin_request_id").value = "";
    }
  } catch (error) {
    hideLoading();
    showToast("Error denying request.", "error");
  }
}

async function deleteRequest() {
  const requestId = document.getElementById("admin_request_id").value;
  if (!requestId) {
    showToast("Please enter a request ID.", "warning");
    return;
  }

  if (!confirm(`Are you sure you want to delete request ${requestId}?`)) {
    return;
  }

  showLoading();

  try {
    const result = await apiCall(`/api/admin/delete/${requestId}`, "DELETE");
    hideLoading();

    showToast(result.message, result.success ? "success" : "error");
    if (result.success) {
      loadAdminData();
      document.getElementById("admin_request_id").value = "";
    }
  } catch (error) {
    hideLoading();
    showToast("Error deleting request.", "error");
  }
}

async function deleteAllRequests() {
  if (
    !confirm(
      "Are you sure you want to delete ALL requests? This cannot be undone."
    )
  ) {
    return;
  }

  showLoading();

  try {
    const result = await apiCall("/api/admin/delete-all", "DELETE");
    hideLoading();

    showToast(result.message, result.success ? "success" : "error");
    if (result.success) {
      loadAdminData();
    }
  } catch (error) {
    hideLoading();
    showToast("Error deleting all requests.", "error");
  }
}

// Event listeners
document.addEventListener("DOMContentLoaded", function () {
  // Form event listeners
  document
    .getElementById("request-form")
    .addEventListener("submit", handleRequestSubmit);
  document
    .getElementById("status-form")
    .addEventListener("submit", handleStatusCheck);
  document
    .getElementById("admin-login-form")
    .addEventListener("submit", handleAdminLogin);

  // Initialize file input
  updateFileInputText();

  // Show home screen by default
  showHome();
});
