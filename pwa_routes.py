# PWA-specific routes for E-Pass System
from flask import render_template, jsonify, send_from_directory
import os

def register_pwa_routes(app):
    """Register PWA-specific routes with the Flask app"""
    
    @app.route('/offline.html', endpoint='pwa_offline_page')
    def pwa_offline():
        """Serve offline page for PWA"""
        return render_template('offline.html')

    @app.route('/sw.js', endpoint='pwa_service_worker_js')
    def pwa_service_worker():
        """Serve service worker file"""
        return send_from_directory('static', 'sw.js', mimetype='application/javascript')

    @app.route('/manifest.json', endpoint='pwa_manifest_json')
    def pwa_manifest():
        """Serve PWA manifest file"""
        return send_from_directory('static', 'manifest.json', mimetype='application/json')
    
    @app.route('/api/pwa/status', endpoint='pwa_status_api')
    def pwa_status():
        """API endpoint to check PWA status"""
        return jsonify({
            'success': True,
            'pwa_enabled': True,
            'offline_capable': True,
            'push_notifications': True
        })

    @app.route('/api/pwa/subscribe', methods=['POST'], endpoint='pwa_subscribe_api')
    def pwa_subscribe():
        """Handle push notification subscription"""
        # This would typically save the subscription to a database
        # For now, just return success
        return jsonify({
            'success': True,
            'message': 'Push notification subscription successful'
        })
    
    # Create icons directory if it doesn't exist
    icons_dir = os.path.join('static', 'icons')
    if not os.path.exists(icons_dir):
        os.makedirs(icons_dir)
        print(f"Created icons directory: {icons_dir}")
        print("Note: You'll need to add PWA icons to this directory for full functionality")
    
    # Create screenshots directory if it doesn't exist
    screenshots_dir = os.path.join('static', 'screenshots')
    if not os.path.exists(screenshots_dir):
        os.makedirs(screenshots_dir)
        print(f"Created screenshots directory: {screenshots_dir}")
        print("Note: You can add app screenshots to this directory for app store listings")
    
    print("PWA routes registered successfully!")
    return app
