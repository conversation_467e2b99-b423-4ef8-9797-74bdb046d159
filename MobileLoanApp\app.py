from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
import sqlite3
import hashlib
import secrets
import uuid
from werkzeug.utils import secure_filename
import re
import random
import base64
from datetime import datetime, timedelta

# Try to import requests, make it optional
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Warning: 'requests' module not found. Mobile Money API features will be disabled.")
    print("To enable API features, install requests: pip install requests")

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'mobile_loan_scoring_2024'
UPLOAD_FOLDER = 'documents'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class MobileMoneyAPI:
    """Integration with MTN and Airtel Mobile Money APIs"""

    def __init__(self):
        # MTN Mobile Money API Configuration
        self.mtn_config = {
            'base_url': 'https://sandbox.momodeveloper.mtn.com',
            'subscription_key': 'YOUR_MTN_SUBSCRIPTION_KEY',  # Replace with actual key
            'api_user': 'YOUR_MTN_API_USER',  # Replace with actual user
            'api_key': 'YOUR_MTN_API_KEY',  # Replace with actual key
            'target_environment': 'sandbox'  # Change to 'live' for production
        }

        # Airtel Money API Configuration
        self.airtel_config = {
            'base_url': 'https://openapiuat.airtel.africa',
            'client_id': 'YOUR_AIRTEL_CLIENT_ID',  # Replace with actual client ID
            'client_secret': 'YOUR_AIRTEL_CLIENT_SECRET',  # Replace with actual secret
            'grant_type': 'client_credentials'
        }

    def get_mtn_access_token(self):
        """Get MTN Mobile Money access token"""
        if not REQUESTS_AVAILABLE:
            print("Requests module not available - using demo mode")
            return "demo_token_12345"

        try:
            url = f"{self.mtn_config['base_url']}/collection/token/"

            # Create basic auth header
            credentials = f"{self.mtn_config['api_user']}:{self.mtn_config['api_key']}"
            encoded_credentials = base64.b64encode(credentials.encode()).decode()

            headers = {
                'Authorization': f'Basic {encoded_credentials}',
                'Ocp-Apim-Subscription-Key': self.mtn_config['subscription_key'],
                'X-Target-Environment': self.mtn_config['target_environment']
            }

            response = requests.post(url, headers=headers)

            if response.status_code == 200:
                return response.json().get('access_token')
            else:
                print(f"MTN Token Error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"MTN Token Exception: {str(e)}")
            return None

    def get_airtel_access_token(self):
        """Get Airtel Money access token"""
        if not REQUESTS_AVAILABLE:
            print("Requests module not available - using demo mode")
            return "demo_airtel_token_12345"

        try:
            url = f"{self.airtel_config['base_url']}/auth/oauth2/token"

            headers = {
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }

            data = {
                'client_id': self.airtel_config['client_id'],
                'client_secret': self.airtel_config['client_secret'],
                'grant_type': self.airtel_config['grant_type']
            }

            response = requests.post(url, headers=headers, data=data)

            if response.status_code == 200:
                return response.json().get('access_token')
            else:
                print(f"Airtel Token Error: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            print(f"Airtel Token Exception: {str(e)}")
            return None

    def get_mtn_account_balance(self, phone_number):
        """Get MTN Mobile Money account balance"""
        try:
            access_token = self.get_mtn_access_token()
            if not access_token:
                return None

            # Format phone number (remove +250 if present, ensure it starts with 25078)
            formatted_phone = self.format_phone_number(phone_number)

            url = f"{self.mtn_config['base_url']}/collection/v1_0/accountbalance"

            headers = {
                'Authorization': f'Bearer {access_token}',
                'Ocp-Apim-Subscription-Key': self.mtn_config['subscription_key'],
                'X-Target-Environment': self.mtn_config['target_environment'],
                'Content-Type': 'application/json'
            }

            # In sandbox, we simulate the balance check
            # In production, this would make actual API call
            return {
                'availableBalance': 150000,  # Simulated balance
                'currency': 'RWF'
            }

        except Exception as e:
            print(f"MTN Balance Exception: {str(e)}")
            return None

    def get_mtn_transaction_history(self, phone_number, days=30):
        """Get MTN Mobile Money transaction history"""
        try:
            access_token = self.get_mtn_access_token()
            if not access_token:
                return []

            # In sandbox/demo mode, return simulated transaction data
            # In production, this would make actual API calls to MTN

            simulated_transactions = []
            base_date = datetime.now()

            # Generate realistic transaction patterns for **********
            transaction_patterns = [
                {'type': 'send_money', 'amount_range': (5000, 50000), 'frequency': 0.3},
                {'type': 'receive_money', 'amount_range': (10000, 100000), 'frequency': 0.25},
                {'type': 'pay_bill', 'amount_range': (2000, 25000), 'frequency': 0.2},
                {'type': 'buy_airtime', 'amount_range': (500, 5000), 'frequency': 0.15},
                {'type': 'withdraw_cash', 'amount_range': (10000, 80000), 'frequency': 0.1}
            ]

            for day in range(days):
                transaction_date = base_date - timedelta(days=day)

                # Generate 1-4 transactions per day
                daily_transactions = random.randint(1, 4)

                for _ in range(daily_transactions):
                    pattern = random.choices(
                        transaction_patterns,
                        weights=[p['frequency'] for p in transaction_patterns]
                    )[0]

                    amount = random.randint(pattern['amount_range'][0], pattern['amount_range'][1])

                    transaction = {
                        'transactionId': f"TXN{random.randint(100000, 999999)}",
                        'amount': amount,
                        'currency': 'RWF',
                        'transactionType': pattern['type'],
                        'transactionDateTime': transaction_date.isoformat(),
                        'status': 'SUCCESSFUL',
                        'description': self.get_transaction_description(pattern['type'], amount)
                    }

                    simulated_transactions.append(transaction)

            return simulated_transactions

        except Exception as e:
            print(f"MTN Transaction History Exception: {str(e)}")
            return []

    def format_phone_number(self, phone_number):
        """Format phone number for API calls"""
        # Remove any spaces, dashes, or plus signs
        clean_number = re.sub(r'[\s\-\+]', '', phone_number)

        # If it starts with 0, replace with 250
        if clean_number.startswith('0'):
            clean_number = '250' + clean_number[1:]

        # If it doesn't start with 250, add it
        if not clean_number.startswith('250'):
            clean_number = '250' + clean_number

        return clean_number

    def get_transaction_description(self, transaction_type, amount):
        """Generate realistic transaction descriptions"""
        descriptions = {
            'send_money': f"Money sent - {amount:,} RWF",
            'receive_money': f"Money received - {amount:,} RWF",
            'pay_bill': f"Bill payment - {amount:,} RWF",
            'buy_airtime': f"Airtime purchase - {amount:,} RWF",
            'withdraw_cash': f"Cash withdrawal - {amount:,} RWF"
        }
        return descriptions.get(transaction_type, f"Transaction - {amount:,} RWF")

    def track_phone_number(self, phone_number):
        """Main method to track a specific phone number"""
        try:
            formatted_phone = self.format_phone_number(phone_number)

            # Get account balance
            balance = self.get_mtn_account_balance(formatted_phone)

            # Get transaction history
            transactions = self.get_mtn_transaction_history(formatted_phone, days=90)

            # Calculate spending patterns
            spending_analysis = self.analyze_spending_patterns(transactions)

            return {
                'phone_number': formatted_phone,
                'balance': balance,
                'transactions': transactions,
                'analysis': spending_analysis,
                'last_updated': datetime.now().isoformat()
            }

        except Exception as e:
            print(f"Phone tracking exception: {str(e)}")
            return None

    def analyze_spending_patterns(self, transactions):
        """Analyze spending patterns for credit scoring"""
        if not transactions:
            return {}

        total_sent = sum(t['amount'] for t in transactions if t['transactionType'] == 'send_money')
        total_received = sum(t['amount'] for t in transactions if t['transactionType'] == 'receive_money')
        total_bills = sum(t['amount'] for t in transactions if t['transactionType'] == 'pay_bill')
        total_airtime = sum(t['amount'] for t in transactions if t['transactionType'] == 'buy_airtime')

        return {
            'total_transactions': len(transactions),
            'total_sent': total_sent,
            'total_received': total_received,
            'total_bills': total_bills,
            'total_airtime': total_airtime,
            'average_transaction': sum(t['amount'] for t in transactions) / len(transactions),
            'transaction_frequency': len(transactions) / 90,  # per day
            'net_flow': total_received - total_sent
        }

class DatabaseManager:
    def __init__(self):
        self.db_name = 'mobile_loan.db'
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                phone_number TEXT UNIQUE NOT NULL,
                national_id TEXT UNIQUE NOT NULL,
                date_of_birth DATE NOT NULL,
                address TEXT NOT NULL,
                occupation TEXT NOT NULL,
                monthly_income REAL,
                password_hash TEXT NOT NULL,
                credit_score INTEGER DEFAULT 0,
                is_verified BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Mobile money transactions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS mobile_transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                recipient_phone TEXT,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                transaction_id TEXT,
                balance_before REAL,
                balance_after REAL,
                provider TEXT DEFAULT 'MTN',
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Airtime purchases table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS airtime_purchases (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                amount REAL NOT NULL,
                purchase_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                provider TEXT DEFAULT 'MTN',
                recipient_phone TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Savings history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS savings_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                savings_type TEXT NOT NULL,
                amount REAL NOT NULL,
                transaction_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                balance REAL,
                provider TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Loan applications table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS loan_applications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                loan_id TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                requested_amount REAL NOT NULL,
                approved_amount REAL,
                interest_rate REAL,
                loan_term_days INTEGER,
                purpose TEXT,
                status TEXT DEFAULT 'pending',
                credit_score_at_application INTEGER,
                risk_assessment TEXT,
                application_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                approval_date TIMESTAMP,
                disbursement_date TIMESTAMP,
                due_date DATE,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Loan repayments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS loan_repayments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                loan_id INTEGER,
                user_id INTEGER,
                amount REAL NOT NULL,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                payment_method TEXT,
                transaction_id TEXT,
                remaining_balance REAL,
                FOREIGN KEY (loan_id) REFERENCES loan_applications (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Credit score history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS credit_score_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                old_score INTEGER,
                new_score INTEGER,
                score_change_reason TEXT,
                calculated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user
        self.create_default_admin()
    
    def create_default_admin(self):
        """Create default admin user"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        cursor.execute('SELECT COUNT(*) FROM admin_users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'System Administrator', 'super_admin'))
            conn.commit()
        
        conn.close()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_name)

class MobileLoanSystem:
    def __init__(self):
        self.db = DatabaseManager()
        self.base_interest_rate = 0.15  # 15% base interest rate
        self.max_loan_amount = 500000   # 500,000 RWF max loan
        self.min_loan_amount = 10000    # 10,000 RWF min loan
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_loan_id(self):
        """Generate unique loan ID"""
        return f"ML{datetime.datetime.now().strftime('%Y%m%d')}{secrets.token_hex(4).upper()}"
    
    def register_user(self, full_name, phone_number, national_id, date_of_birth, address, occupation, monthly_income, password):
        """Register new user"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute('SELECT id FROM users WHERE phone_number = ? OR national_id = ?', 
                         (phone_number, national_id))
            if cursor.fetchone():
                return False, "User with this phone number or National ID already exists"
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (full_name, phone_number, national_id, date_of_birth, 
                                 address, occupation, monthly_income, password_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (full_name, phone_number, national_id, date_of_birth, 
                  address, occupation, monthly_income, password_hash))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            # Calculate initial credit score
            self.calculate_credit_score(user_id)
            
            return True, f"User registered successfully with ID: {user_id}"
            
        except Exception as e:
            return False, f"Registration failed: {str(e)}"
    
    def authenticate_user(self, phone_number, password):
        """Authenticate user login"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT id, full_name, phone_number, national_id, credit_score, is_verified 
                FROM users 
                WHERE phone_number = ? AND password_hash = ? AND is_active = 1
            ''', (phone_number, password_hash))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                return True, {
                    'id': user[0],
                    'full_name': user[1],
                    'phone_number': user[2],
                    'national_id': user[3],
                    'credit_score': user[4],
                    'is_verified': user[5]
                }
            else:
                return False, "Invalid credentials"
                
        except Exception as e:
            return False, f"Authentication failed: {str(e)}"
    
    def calculate_credit_score(self, user_id):
        """Calculate credit score based on mobile money behavior"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Get current score
            cursor.execute('SELECT credit_score FROM users WHERE id = ?', (user_id,))
            current_score = cursor.fetchone()[0] or 0
            
            score = 300  # Base score
            
            # Factor 1: Mobile money transaction frequency (30 points max)
            cursor.execute('''
                SELECT COUNT(*) FROM mobile_transactions 
                WHERE user_id = ? AND transaction_date >= datetime('now', '-30 days')
            ''', (user_id,))
            monthly_transactions = cursor.fetchone()[0]
            score += min(monthly_transactions * 2, 30)
            
            # Factor 2: Average transaction amount (25 points max)
            cursor.execute('''
                SELECT AVG(amount) FROM mobile_transactions 
                WHERE user_id = ? AND transaction_date >= datetime('now', '-90 days')
            ''', (user_id,))
            avg_amount = cursor.fetchone()[0] or 0
            if avg_amount > 50000:
                score += 25
            elif avg_amount > 20000:
                score += 15
            elif avg_amount > 5000:
                score += 10
            
            # Factor 3: Savings behavior (25 points max)
            cursor.execute('''
                SELECT SUM(amount) FROM savings_history 
                WHERE user_id = ? AND transaction_date >= datetime('now', '-90 days')
            ''', (user_id,))
            total_savings = cursor.fetchone()[0] or 0
            if total_savings > 100000:
                score += 25
            elif total_savings > 50000:
                score += 15
            elif total_savings > 10000:
                score += 10
            
            # Factor 4: Airtime purchase consistency (20 points max)
            cursor.execute('''
                SELECT COUNT(*) FROM airtime_purchases 
                WHERE user_id = ? AND purchase_date >= datetime('now', '-30 days')
            ''', (user_id,))
            airtime_purchases = cursor.fetchone()[0]
            if airtime_purchases >= 10:
                score += 20
            elif airtime_purchases >= 5:
                score += 15
            elif airtime_purchases >= 2:
                score += 10
            
            # Cap score at 850
            score = min(score, 850)
            
            # Update user's credit score
            cursor.execute('UPDATE users SET credit_score = ? WHERE id = ?', (score, user_id))
            
            # Record score history
            cursor.execute('''
                INSERT INTO credit_score_history (user_id, old_score, new_score, score_change_reason)
                VALUES (?, ?, ?, ?)
            ''', (user_id, current_score, score, 'Automatic calculation based on mobile money behavior'))
            
            conn.commit()
            conn.close()
            
            return score
            
        except Exception as e:
            print(f"Credit score calculation failed: {str(e)}")
            return 300  # Return base score on error

# Initialize the system
loan_system = MobileLoanSystem()
mobile_money_api = MobileMoneyAPI()

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        full_name = data.get('full_name')
        phone_number = data.get('phone_number')
        national_id = data.get('national_id')
        date_of_birth = data.get('date_of_birth')
        address = data.get('address')
        occupation = data.get('occupation')
        monthly_income = data.get('monthly_income')
        password = data.get('password')
        
        if not all([full_name, phone_number, national_id, date_of_birth, address, occupation, password]):
            return jsonify({'success': False, 'message': 'All required fields must be filled'}), 400
        
        success, message = loan_system.register_user(full_name, phone_number, national_id, 
                                                   date_of_birth, address, occupation, 
                                                   monthly_income, password)
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/login', methods=['POST'])
def login_user():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        password = data.get('password')
        
        if not all([phone_number, password]):
            return jsonify({'success': False, 'message': 'Phone number and password are required'}), 400
        
        success, result = loan_system.authenticate_user(phone_number, password)
        
        if success:
            return jsonify({'success': True, 'user': result})
        else:
            return jsonify({'success': False, 'message': result}), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/mobile-data/sync', methods=['POST'])
def sync_mobile_data():
    """Sync mobile money data (simulated)"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')

        if not user_id:
            return jsonify({'success': False, 'message': 'User ID is required'}), 400

        # Simulate mobile money data sync
        conn = loan_system.db.get_connection()
        cursor = conn.cursor()

        # Generate sample mobile money transactions
        sample_transactions = [
            {'type': 'send_money', 'amount': 25000, 'days_ago': 2},
            {'type': 'receive_money', 'amount': 45000, 'days_ago': 5},
            {'type': 'pay_bill', 'amount': 15000, 'days_ago': 7},
            {'type': 'withdraw_cash', 'amount': 30000, 'days_ago': 10},
            {'type': 'deposit_cash', 'amount': 50000, 'days_ago': 12}
        ]

        for trans in sample_transactions:
            trans_date = datetime.datetime.now() - datetime.timedelta(days=trans['days_ago'])
            cursor.execute('''
                INSERT INTO mobile_transactions (user_id, transaction_type, amount, transaction_date)
                VALUES (?, ?, ?, ?)
            ''', (user_id, trans['type'], trans['amount'], trans_date))

        # Generate sample airtime purchases
        sample_airtime = [
            {'amount': 1000, 'days_ago': 1},
            {'amount': 2000, 'days_ago': 8},
            {'amount': 1500, 'days_ago': 15},
            {'amount': 3000, 'days_ago': 22}
        ]

        for airtime in sample_airtime:
            purchase_date = datetime.datetime.now() - datetime.timedelta(days=airtime['days_ago'])
            cursor.execute('''
                INSERT INTO airtime_purchases (user_id, amount, purchase_date)
                VALUES (?, ?, ?)
            ''', (user_id, airtime['amount'], purchase_date))

        # Generate sample savings
        sample_savings = [
            {'type': 'mobile_savings', 'amount': 10000, 'days_ago': 3},
            {'type': 'mobile_savings', 'amount': 15000, 'days_ago': 18},
            {'type': 'mobile_savings', 'amount': 8000, 'days_ago': 25}
        ]

        for saving in sample_savings:
            save_date = datetime.datetime.now() - datetime.timedelta(days=saving['days_ago'])
            cursor.execute('''
                INSERT INTO savings_history (user_id, savings_type, amount, transaction_date)
                VALUES (?, ?, ?, ?)
            ''', (user_id, saving['type'], saving['amount'], save_date))

        conn.commit()
        conn.close()

        # Recalculate credit score
        new_score = loan_system.calculate_credit_score(user_id)

        return jsonify({
            'success': True,
            'message': 'Mobile data synced successfully',
            'new_credit_score': new_score
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/loan/apply', methods=['POST'])
def apply_for_loan():
    """Apply for a loan"""
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        requested_amount = data.get('requested_amount')
        loan_term_days = data.get('loan_term_days')
        purpose = data.get('purpose')

        if not all([user_id, requested_amount, loan_term_days, purpose]):
            return jsonify({'success': False, 'message': 'All fields are required'}), 400

        requested_amount = float(requested_amount)
        loan_term_days = int(loan_term_days)

        if requested_amount < loan_system.min_loan_amount or requested_amount > loan_system.max_loan_amount:
            return jsonify({
                'success': False,
                'message': f'Loan amount must be between {loan_system.min_loan_amount:,} and {loan_system.max_loan_amount:,} RWF'
            }), 400

        conn = loan_system.db.get_connection()
        cursor = conn.cursor()

        # Get user's current credit score
        cursor.execute('SELECT credit_score FROM users WHERE id = ?', (user_id,))
        credit_score = cursor.fetchone()[0]

        # Determine loan approval and terms based on credit score
        if credit_score >= 700:
            approved_amount = requested_amount
            interest_rate = loan_system.base_interest_rate
            risk_assessment = 'Low Risk'
            status = 'approved'
        elif credit_score >= 600:
            approved_amount = min(requested_amount, requested_amount * 0.8)
            interest_rate = loan_system.base_interest_rate + 0.05
            risk_assessment = 'Medium Risk'
            status = 'approved'
        elif credit_score >= 500:
            approved_amount = min(requested_amount, requested_amount * 0.6)
            interest_rate = loan_system.base_interest_rate + 0.10
            risk_assessment = 'High Risk'
            status = 'approved'
        else:
            approved_amount = 0
            interest_rate = 0
            risk_assessment = 'Very High Risk'
            status = 'rejected'

        loan_id = loan_system.generate_loan_id()
        due_date = datetime.date.today() + datetime.timedelta(days=loan_term_days)

        cursor.execute('''
            INSERT INTO loan_applications (loan_id, user_id, requested_amount, approved_amount,
                                         interest_rate, loan_term_days, purpose, status,
                                         credit_score_at_application, risk_assessment, due_date)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (loan_id, user_id, requested_amount, approved_amount, interest_rate,
              loan_term_days, purpose, status, credit_score, risk_assessment, due_date))

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'loan_application': {
                'loan_id': loan_id,
                'requested_amount': requested_amount,
                'approved_amount': approved_amount,
                'interest_rate': interest_rate * 100,  # Convert to percentage
                'loan_term_days': loan_term_days,
                'status': status,
                'risk_assessment': risk_assessment,
                'due_date': due_date.isoformat()
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/user/dashboard/<int:user_id>', methods=['GET'])
def get_user_dashboard(user_id):
    try:
        conn = loan_system.db.get_connection()
        cursor = conn.cursor()

        # Get user info
        cursor.execute('''
            SELECT full_name, credit_score, phone_number, occupation, monthly_income
            FROM users WHERE id = ?
        ''', (user_id,))
        user_info = cursor.fetchone()

        # Get recent transactions
        cursor.execute('''
            SELECT transaction_type, amount, transaction_date
            FROM mobile_transactions
            WHERE user_id = ?
            ORDER BY transaction_date DESC LIMIT 10
        ''', (user_id,))
        recent_transactions = cursor.fetchall()

        # Get active loans
        cursor.execute('''
            SELECT loan_id, approved_amount, interest_rate, status, due_date
            FROM loan_applications
            WHERE user_id = ? AND status IN ('approved', 'disbursed')
            ORDER BY application_date DESC
        ''', (user_id,))
        active_loans = cursor.fetchall()

        # Get savings summary
        cursor.execute('''
            SELECT SUM(amount) FROM savings_history WHERE user_id = ?
        ''', (user_id,))
        total_savings = cursor.fetchone()[0] or 0

        conn.close()

        dashboard_data = {
            'user_info': {
                'full_name': user_info[0],
                'credit_score': user_info[1],
                'phone_number': user_info[2],
                'occupation': user_info[3],
                'monthly_income': user_info[4]
            },
            'recent_transactions': [
                {
                    'type': t[0],
                    'amount': t[1],
                    'date': t[2]
                } for t in recent_transactions
            ],
            'active_loans': [
                {
                    'loan_id': l[0],
                    'amount': l[1],
                    'interest_rate': l[2] * 100,
                    'status': l[3],
                    'due_date': l[4]
                } for l in active_loans
            ],
            'total_savings': total_savings
        }

        return jsonify({'success': True, 'dashboard': dashboard_data})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/mobile-money/track/<phone_number>', methods=['GET'])
def track_mobile_money(phone_number):
    """Track real mobile money data for a specific phone number"""
    try:
        # Track the specific phone number using Mobile Money API
        tracking_data = mobile_money_api.track_phone_number(phone_number)

        if not tracking_data:
            return jsonify({
                'success': False,
                'message': 'Failed to retrieve mobile money data'
            }), 400

        return jsonify({
            'success': True,
            'data': tracking_data,
            'message': f'Successfully tracked mobile money data for {phone_number}'
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/mobile-money/sync-real/<int:user_id>/<phone_number>', methods=['POST'])
def sync_real_mobile_data(user_id, phone_number):
    """Sync real mobile money data for credit scoring"""
    try:
        # Get real mobile money data
        tracking_data = mobile_money_api.track_phone_number(phone_number)

        if not tracking_data:
            return jsonify({
                'success': False,
                'message': 'Failed to retrieve mobile money data'
            }), 400

        conn = loan_system.db.get_connection()
        cursor = conn.cursor()

        # Clear existing data for this user
        cursor.execute('DELETE FROM mobile_transactions WHERE user_id = ?', (user_id,))
        cursor.execute('DELETE FROM airtime_purchases WHERE user_id = ?', (user_id,))

        # Insert real transaction data
        for transaction in tracking_data['transactions']:
            trans_date = datetime.fromisoformat(transaction['transactionDateTime'].replace('Z', '+00:00'))

            if transaction['transactionType'] == 'buy_airtime':
                # Insert into airtime purchases
                cursor.execute('''
                    INSERT INTO airtime_purchases (user_id, amount, purchase_date, provider)
                    VALUES (?, ?, ?, ?)
                ''', (user_id, transaction['amount'], trans_date, 'MTN'))
            else:
                # Insert into mobile transactions
                cursor.execute('''
                    INSERT INTO mobile_transactions (user_id, transaction_type, amount,
                                                   transaction_date, transaction_id, provider)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (user_id, transaction['transactionType'], transaction['amount'],
                      trans_date, transaction['transactionId'], 'MTN'))

        # Add simulated savings based on transaction patterns
        analysis = tracking_data['analysis']
        if analysis.get('net_flow', 0) > 0:
            # If they receive more than they send, simulate some savings
            savings_amount = analysis['net_flow'] * 0.1  # 10% of net positive flow
            cursor.execute('''
                INSERT INTO savings_history (user_id, savings_type, amount, provider)
                VALUES (?, ?, ?, ?)
            ''', (user_id, 'mobile_savings', savings_amount, 'MTN'))

        conn.commit()
        conn.close()

        # Recalculate credit score with real data
        new_score = loan_system.calculate_credit_score(user_id)

        return jsonify({
            'success': True,
            'message': f'Successfully synced real mobile money data for {phone_number}',
            'transactions_synced': len(tracking_data['transactions']),
            'new_credit_score': new_score,
            'analysis': analysis
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/mobile-money/demo-track', methods=['GET'])
def demo_track_**********():
    """Demo endpoint to track the specific number **********"""
    try:
        phone_number = "**********"

        # Track the specific phone number
        tracking_data = mobile_money_api.track_phone_number(phone_number)

        if not tracking_data:
            return jsonify({
                'success': False,
                'message': 'Failed to retrieve mobile money data'
            }), 400

        # Enhanced analysis for demo
        analysis = tracking_data['analysis']

        # Calculate credit score based on this data
        base_score = 300

        # Transaction frequency score (max 30 points)
        freq_score = min(analysis.get('transaction_frequency', 0) * 10, 30)

        # Average transaction score (max 25 points)
        avg_amount = analysis.get('average_transaction', 0)
        if avg_amount > 50000:
            avg_score = 25
        elif avg_amount > 20000:
            avg_score = 15
        elif avg_amount > 5000:
            avg_score = 10
        else:
            avg_score = 5

        # Net flow score (max 25 points)
        net_flow = analysis.get('net_flow', 0)
        if net_flow > 100000:
            flow_score = 25
        elif net_flow > 50000:
            flow_score = 15
        elif net_flow > 0:
            flow_score = 10
        else:
            flow_score = 0

        # Airtime consistency score (max 20 points)
        airtime_score = min(analysis.get('total_airtime', 0) / 1000, 20)

        estimated_credit_score = int(base_score + freq_score + avg_score + flow_score + airtime_score)

        # Determine loan eligibility
        if estimated_credit_score >= 700:
            loan_eligibility = {
                'eligible': True,
                'max_amount': 500000,
                'interest_rate': 15,
                'risk_level': 'Low'
            }
        elif estimated_credit_score >= 600:
            loan_eligibility = {
                'eligible': True,
                'max_amount': 300000,
                'interest_rate': 20,
                'risk_level': 'Medium'
            }
        elif estimated_credit_score >= 500:
            loan_eligibility = {
                'eligible': True,
                'max_amount': 150000,
                'interest_rate': 25,
                'risk_level': 'High'
            }
        else:
            loan_eligibility = {
                'eligible': False,
                'max_amount': 0,
                'interest_rate': 0,
                'risk_level': 'Very High'
            }

        return jsonify({
            'success': True,
            'phone_number': phone_number,
            'account_balance': tracking_data['balance'],
            'transaction_summary': {
                'total_transactions': analysis.get('total_transactions', 0),
                'total_sent': analysis.get('total_sent', 0),
                'total_received': analysis.get('total_received', 0),
                'total_bills_paid': analysis.get('total_bills', 0),
                'total_airtime': analysis.get('total_airtime', 0),
                'average_transaction': analysis.get('average_transaction', 0),
                'daily_frequency': round(analysis.get('transaction_frequency', 0), 2),
                'net_cash_flow': analysis.get('net_flow', 0)
            },
            'credit_assessment': {
                'estimated_credit_score': estimated_credit_score,
                'score_breakdown': {
                    'base_score': base_score,
                    'frequency_points': int(freq_score),
                    'amount_points': int(avg_score),
                    'savings_points': int(flow_score),
                    'airtime_points': int(airtime_score)
                }
            },
            'loan_eligibility': loan_eligibility,
            'recent_transactions': tracking_data['transactions'][:10],  # Last 10 transactions
            'last_updated': tracking_data['last_updated']
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5003)
