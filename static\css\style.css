/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
  border-radius: 15px;
  margin-top: 1rem;
}

.nav-brand {
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
}

.nav-brand i {
  margin-right: 0.5rem;
  font-size: 1.8rem;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  text-decoration: none;
  color: #666;
  font-weight: 500;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  transition: all 0.3s ease;
}

.nav-link:hover,
.nav-link.active {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

/* Screen Management */
.screen {
  display: none;
  animation: fadeIn 0.5s ease-in-out;
}

.screen.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 3rem 0;
  color: white;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.2rem;
  margin-bottom: 3rem;
  opacity: 0.9;
}

.hero-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 1.5rem;
  border-radius: 15px;
  text-align: center;
  min-width: 120px;
}

.stat-card i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  color: #ffd700;
}

.stat-card h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

/* Action Cards */
.action-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

.card {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.card-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.card h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #333;
}

.card p {
  color: #666;
  margin-bottom: 1.5rem;
}

.card-arrow {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 1.2rem;
  color: #667eea;
  opacity: 0;
  transition: all 0.3s ease;
}

.card:hover .card-arrow {
  opacity: 1;
  transform: translateX(5px);
}

/* Form Styles */
.form-container {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  max-width: 600px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 2rem;
}

.form-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
}

.form-header p {
  color: #666;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #333;
}

.form-group label i {
  margin-right: 0.5rem;
  color: #667eea;
  width: 20px;
}

.form-group input,
.form-group textarea {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.file-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.file-input-wrapper input[type="file"] {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-input-text {
  padding: 0.75rem;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  background: #f8f9fa;
  color: #666;
  width: 100%;
  cursor: pointer;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  justify-content: center;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover {
  background: #218838;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover {
  background: #e0a800;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: space-between;
  margin-top: 1rem;
}

/* Status Result */
.status-result {
  margin-top: 2rem;
  padding: 1.5rem;
  border-radius: 10px;
  background: #f8f9fa;
  border-left: 4px solid #667eea;
}

.status-approved {
  background: #d4edda;
  border-left-color: #28a745;
  color: #155724;
}

.status-denied {
  background: #f8d7da;
  border-left-color: #dc3545;
  color: #721c24;
}

.status-pending {
  background: #fff3cd;
  border-left-color: #ffc107;
  color: #856404;
}

/* Admin Panel */
.admin-container {
  background: white;
  padding: 2rem;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.admin-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid #e1e5e9;
}

.admin-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.admin-title-section h2 {
  margin: 0;
}

.logout-btn {
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
}

.admin-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.admin-stat {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
  min-width: 120px;
}

.admin-controls {
  margin-bottom: 2rem;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.control-group label {
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.control-group input {
  padding: 0.5rem;
  border: 2px solid #e1e5e9;
  border-radius: 5px;
  width: 120px;
}

.control-buttons {
  display: flex;
  gap: 0.5rem;
}

.danger-zone {
  display: flex;
  gap: 1rem;
  justify-content: center;
  padding-top: 1rem;
  border-top: 2px solid #e1e5e9;
}

/* Requests List */
.requests-container h3 {
  margin-bottom: 1rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.requests-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #e1e5e9;
  border-radius: 10px;
  padding: 1rem;
}

.request-item {
  background: #f8f9fa;
  padding: 1rem;
  margin-bottom: 1rem;
  border-radius: 10px;
  border-left: 4px solid #667eea;
}

.request-item.approved {
  border-left-color: #28a745;
}

.request-item.denied {
  border-left-color: #dc3545;
}

.request-item.pending {
  border-left-color: #ffc107;
}

.request-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.request-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.request-summary {
  color: #666;
  font-size: 0.9rem;
}

/* Loading and Toast */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-spinner {
  background: white;
  padding: 2rem;
  border-radius: 15px;
  text-align: center;
}

.loading-spinner i {
  font-size: 2rem;
  color: #667eea;
  margin-bottom: 1rem;
}

.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
}

.toast {
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  margin-bottom: 1rem;
  border-left: 4px solid #667eea;
  animation: slideIn 0.3s ease;
}

.toast.success {
  border-left-color: #28a745;
}

.toast.error {
  border-left-color: #dc3545;
}

.toast.warning {
  border-left-color: #ffc107;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-links {
    gap: 1rem;
  }

  .hero-content h1 {
    font-size: 2rem;
  }

  .hero-stats {
    flex-direction: column;
    align-items: center;
  }

  .action-cards {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }

  .control-group {
    flex-direction: column;
    align-items: stretch;
  }

  .control-buttons {
    justify-content: center;
  }

  .danger-zone {
    flex-direction: column;
  }

  .admin-title-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .request-actions {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  animation: slideUp 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 2px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 15px 15px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #666;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #e1e5e9;
  color: #333;
}

.modal-body {
  padding: 1.5rem;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.detail-item label i {
  color: #667eea;
  width: 16px;
}

.detail-item span {
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e1e5e9;
}

.reason-text {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e1e5e9;
  line-height: 1.6;
}

.attachment-section {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: #f8f9fa;
  border-radius: 5px;
  border: 1px solid #e1e5e9;
}

.attachment-name {
  flex: 1;
  font-weight: 500;
}

.no-attachment {
  color: #666;
  font-style: italic;
}

.modal-footer {
  padding: 1rem 1.5rem;
  border-top: 2px solid #e1e5e9;
  background: #f8f9fa;
  border-radius: 0 0 15px 15px;
  text-align: right;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
