/* PWA-specific styles for E-Pass System */

/* PWA Install Button */
.pwa-install-btn {
  margin-left: 1rem;
  font-size: 0.9rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
}

.pwa-install-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Offline Indicator */
.offline-indicator {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: #ff6b6b;
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-size: 0.9rem;
  z-index: 10000;
  display: none;
  animation: slideDown 0.3s ease;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Update Banner */
.update-banner {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: #4f46e5;
  color: white;
  border-radius: 10px;
  padding: 1rem;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  z-index: 10000;
  animation: slideUp 0.3s ease;
}

.update-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.update-content .btn {
  padding: 0.3rem 0.8rem;
  font-size: 0.8rem;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* Mobile-First Responsive Design */
@media (max-width: 768px) {
  /* Navigation adjustments */
  .navbar {
    flex-direction: column;
    padding: 1rem;
    gap: 1rem;
  }

  .nav-links {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
  }

  .nav-link {
    padding: 0.8rem;
    text-align: center;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
  }

  .pwa-install-btn {
    margin-left: 0;
    margin-top: 0.5rem;
    width: 100%;
  }

  /* Container adjustments */
  .container {
    padding: 0.5rem;
  }

  /* Hero section mobile optimization */
  .hero-section {
    padding: 2rem 1rem;
  }

  .hero-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .stat-card {
    min-width: auto;
    width: 100%;
  }

  /* Form adjustments */
  .form-container {
    padding: 1.5rem;
    margin: 0.5rem;
  }

  .form-group {
    margin-bottom: 1.5rem;
  }

  .form-group input,
  .form-group textarea,
  .form-group select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 1rem;
  }

  .form-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .form-actions .btn {
    width: 100%;
    padding: 1rem;
    font-size: 1rem;
  }

  /* Admin panel mobile optimization */
  .admin-container {
    padding: 1rem;
    margin: 0.5rem;
  }

  .admin-title-section {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .admin-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .admin-stat {
    min-width: auto;
  }

  .control-group {
    flex-direction: column;
    gap: 1rem;
  }

  .control-buttons {
    flex-direction: column;
    gap: 0.5rem;
  }

  .control-buttons .btn {
    width: 100%;
    padding: 1rem;
  }

  /* Request cards mobile optimization */
  .requests-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .request-card {
    padding: 1rem;
  }

  .request-actions {
    flex-direction: column;
    gap: 0.5rem;
  }

  .request-actions .btn {
    width: 100%;
  }

  /* Status result mobile optimization */
  .status-result {
    padding: 1rem;
    margin: 0.5rem;
  }

  /* Cards grid mobile optimization */
  .cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .card {
    padding: 1.5rem;
  }
}

/* Tablet adjustments */
@media (min-width: 769px) and (max-width: 1024px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .requests-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .admin-stats {
    flex-wrap: wrap;
    justify-content: center;
  }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
  /* Increase touch targets */
  .btn {
    min-height: 44px;
    padding: 0.8rem 1.5rem;
  }

  .nav-link {
    min-height: 44px;
    padding: 0.8rem 1rem;
  }

  .card {
    cursor: pointer;
    transition: transform 0.2s ease;
  }

  .card:active {
    transform: scale(0.98);
  }

  /* Remove hover effects on touch devices */
  .card:hover {
    transform: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }

  .btn:hover {
    transform: none;
  }
}

/* PWA-specific display mode styles */
@media (display-mode: standalone) {
  /* Add padding for status bar on mobile */
  body {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Hide install button when in standalone mode */
  .pwa-install-btn {
    display: none !important;
  }

  /* Adjust navbar for standalone mode */
  .navbar {
    padding-top: calc(1rem + env(safe-area-inset-top));
  }
}

/* Dark mode support for PWA */
@media (prefers-color-scheme: dark) {
  .offline-indicator {
    background: #dc2626;
  }

  .update-banner {
    background: #3730a3;
  }

  .pwa-install-btn {
    background: linear-gradient(135deg, #4338ca 0%, #5b21b6 100%);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .offline-indicator,
  .update-banner,
  .card,
  .btn {
    animation: none;
    transition: none;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .pwa-install-btn {
    background: #000;
    border: 2px solid #fff;
  }

  .offline-indicator {
    background: #000;
    border: 2px solid #fff;
  }

  .update-banner {
    background: #000;
    border: 2px solid #fff;
  }
}
