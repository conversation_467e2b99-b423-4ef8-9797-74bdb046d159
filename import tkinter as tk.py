from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
from difflib import SequenceMatcher
import re
from werkzeug.utils import secure_filename


class DuplicateDetectionAI:
    """AI system to detect duplicate e-pass requests within a 24-hour period."""

    def __init__(self):
        self.similarity_threshold = 0.8  # Similarity threshold for text matching
        self.time_window_hours = 24  # Time window in hours

    def calculate_text_similarity(self, text1, text2):
        """Calculate similarity ratio between two text strings."""
        return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()

    def normalize_contact(self, contact):
        """Normalize contact information by removing non-digit characters."""
        return re.sub(r'\D', '', str(contact))

    def is_potential_duplicate(self, new_request, existing_requests):
        """
        Check if a new request is potentially a duplicate.
        Returns (is_duplicate, duplicate_request_id, confidence_score, detection_reason)
        """
        # Extract current timestamp
        current_time = datetime.datetime.now()

        # Get relevant fields from the new request
        new_name = str(new_request["name"]).lower()
        new_contact = self.normalize_contact(new_request["contact"])
        new_id_proof = str(new_request["id_proof"]).lower()
        new_reason = str(new_request["reason"]).lower()

        highest_confidence = 0
        duplicate_id = None
        detection_reason = ""

        for request in existing_requests:
            # Skip comparing with the same request
            if "request_id" in new_request and request["request_id"] == new_request["request_id"]:
                continue

            # Only check requests within the time window
            if "timestamp" not in request:
                continue

            request_time = datetime.datetime.fromisoformat(request["timestamp"])
            time_diff = (current_time - request_time).total_seconds() / 3600

            if time_diff > self.time_window_hours:
                continue

            # Calculate similarities
            name_similarity = self.calculate_text_similarity(new_name, request["name"].lower())
            contact_similarity = self.calculate_text_similarity(new_contact, self.normalize_contact(request["contact"]))
            id_similarity = self.calculate_text_similarity(new_id_proof, request["id_proof"].lower())
            reason_similarity = self.calculate_text_similarity(new_reason, request["reason"].lower())

            # Weighted confidence score
            confidence = (name_similarity * 0.4 +
                          contact_similarity * 0.3 +
                          id_similarity * 0.2 +
                          reason_similarity * 0.1)

            # Exact contact match is a strong indicator
            if new_contact and new_contact == self.normalize_contact(request["contact"]):
                confidence = max(confidence, 0.7)  # Set minimum confidence to 0.7 for contact match

            # Exact ID proof match is a very strong indicator
            if new_id_proof and new_id_proof == request["id_proof"].lower():
                confidence = max(confidence, 0.9)  # Set minimum confidence to 0.9 for ID match

            # Update if this is the highest confidence duplicate so far
            if confidence > highest_confidence and confidence >= self.similarity_threshold:
                highest_confidence = confidence
                duplicate_id = request["request_id"]

                # Determine the primary reason for the duplicate detection
                if contact_similarity > 0.9:
                    detection_reason = "Same contact information"
                elif name_similarity > 0.9:
                    detection_reason = "Same name"
                elif id_similarity > 0.9:
                    detection_reason = "Same ID proof"
                elif reason_similarity > 0.9:
                    detection_reason = "Very similar reason"
                else:
                    detection_reason = "Multiple matching factors"

        is_duplicate = highest_confidence >= self.similarity_threshold
        return (is_duplicate, duplicate_id, highest_confidence, detection_reason)


class CurfewEPassSystem:
    def __init__(self):
        self.requests = []  # Store all e-pass requests
        self.admin_credentials = {"admin": "admin123"}  # Admin login details
        self.current_request_id = 1  # Auto-increment request ID
        self.data_file = "requests.json"  # File to save requests
        self.duplicate_detector = DuplicateDetectionAI()  # Initialize the AI duplicate detector
        self.load_requests()  # Load requests from file on startup

    def load_requests(self):
        """Load requests from the JSON file."""
        if os.path.exists(self.data_file):
            with open(self.data_file, "r") as file:
                data = json.load(file)
                self.requests = data.get("requests", [])
                self.current_request_id = data.get("current_request_id", 1)

    def save_requests(self):
        """Save requests to the JSON file."""
        data = {
            "requests": self.requests,
            "current_request_id": self.current_request_id,
        }
        with open(self.data_file, "w") as file:
            json.dump(data, file)

    def register_request(self, name, contact, id_proof, reason, attachment):
        # Create the new request
        request = {
            "request_id": self.current_request_id,
            "name": name,
            "contact": contact,
            "id_proof": id_proof,
            "reason": reason,
            "attachment": attachment,
            "status": "Pending",
            "e_pass_id": None,
            "appeal": None,
            "timestamp": datetime.datetime.now().isoformat()  # Add timestamp
        }

        # Check for potential duplicates
        is_duplicate, duplicate_id, confidence, reason = self.duplicate_detector.is_potential_duplicate(
            request, self.requests
        )

        if is_duplicate:
            return (False, f"Potential duplicate of Request #{duplicate_id} detected ({confidence:.0%} confidence). " +
                    f"Reason: {reason}. Please wait 24 hours between similar requests or contact support.")

        # Not a duplicate, proceed with registration
        self.requests.append(request)
        self.current_request_id += 1
        self.save_requests()  # Save requests after adding a new one
        return (True, f"Request submitted successfully! Your request ID is {request['request_id']}.")

    def get_request_status(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                status = f"Request ID: {request_id}\nStatus: {request['status']}\n"
                if request["status"] == "Approved":
                    status += f"E-Pass ID: {request['e_pass_id']}"
                elif request["status"] == "Denied":
                    status += f"\nAppeal: {request['appeal']}" if request["appeal"] else ""
                return status
        return "Request ID not found."

    def approve_request(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                if request["status"] == "Pending":
                    request["status"] = "Approved"
                    request["e_pass_id"] = f"EP-{request_id:05}"
                    self.save_requests()  # Save requests after approval
                    return f"Request {request_id} approved. E-Pass ID: {request['e_pass_id']}"
                return "Request already processed."
        return "Request ID not found."

    def deny_request(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                if request["status"] == "Pending":
                    request["status"] = "Denied"
                    self.save_requests()  # Save requests after denial
                    return f"Request {request_id} denied."
                return "Request already processed."
        return "Request ID not found."

    def delete_request(self, request_id):
        """Delete a request by request ID (only accessible by admin)."""
        for request in self.requests:
            if request["request_id"] == request_id:
                self.requests.remove(request)
                self.save_requests()  # Save after deletion
                return f"Request {request_id} deleted."
        return "Request ID not found."

    def delete_all_requests(self):
        """Delete all requests and reset the request ID to 1."""
        self.requests.clear()  # Clear all requests
        self.current_request_id = 1  # Reset the request ID counter
        self.save_requests()  # Save after clearing all requests
        return "All requests have been deleted, and the ID counter has been reset to 1."

    def get_duplicate_stats(self):
        """Generate statistics about duplicate detection."""
        total_requests = len(self.requests)
        duplicate_count = 0

        # Count the number of duplicates flagged in the past
        for i, request in enumerate(self.requests):
            # Check all previously submitted requests
            for prev_request in self.requests[:i]:
                # Only consider requests within 24 hours of each other
                if "timestamp" in request and "timestamp" in prev_request:
                    req_time = datetime.datetime.fromisoformat(request["timestamp"])
                    prev_time = datetime.datetime.fromisoformat(prev_request["timestamp"])
                    time_diff = (req_time - prev_time).total_seconds() / 3600

                    if 0 <= time_diff <= 24:
                        # Check for similarity
                        is_dup, _, conf, _ = self.duplicate_detector.is_potential_duplicate(
                            request, [prev_request]
                        )
                        if is_dup:
                            duplicate_count += 1
                            break

        return {
            "total_requests": total_requests,
            "potential_duplicates": duplicate_count,
            "duplicate_percentage": (duplicate_count / total_requests * 100) if total_requests > 0 else 0
        }


class EPassGUI:
    def __init__(self, root):
        self.system = CurfewEPassSystem()
        self.root = root
        self.root.title("Curfew E-Pass System")
        self.root.geometry("600x500")
        self.create_home_screen()

    def clear_window(self):
        """Clears the current window for the next screen."""
        for widget in self.root.winfo_children():
            widget.destroy()

    def create_home_screen(self):
        self.clear_window()

        tk.Label(self.root, text="Curfew E-Pass System", font=("Arial", 16, "bold")).pack(pady=10)

        tk.Button(self.root, text="Request E-Pass", command=self.create_request_screen).pack(pady=5)
        tk.Button(self.root, text="Check Request Status", command=self.create_status_screen).pack(pady=5)
        tk.Button(self.root, text="Admin Login", command=self.admin_login_screen).pack(pady=5)

    def create_request_screen(self):
        self.clear_window()

        tk.Label(self.root, text="Request E-Pass", font=("Arial", 16, "bold")).pack(pady=10)

        tk.Label(self.root, text="Name:").pack(pady=5)
        name_entry = tk.Entry(self.root)
        name_entry.pack()

        tk.Label(self.root, text="Contact:").pack(pady=5)
        contact_entry = tk.Entry(self.root)
        contact_entry.pack()

        tk.Label(self.root, text="ID Proof:").pack(pady=5)
        id_proof_entry = tk.Entry(self.root)
        id_proof_entry.pack()

        tk.Label(self.root, text="Reason:").pack(pady=5)
        reason_entry = tk.Entry(self.root)
        reason_entry.pack()

        tk.Label(self.root, text="Attachment (optional):").pack(pady=5)

        def select_file():
            file_path = filedialog.askopenfilename(title="Select a file")
            attachment_label.config(text=file_path)

        attachment_label = tk.Label(self.root, text="No file selected.")
        attachment_label.pack(pady=5)

        tk.Button(self.root, text="Browse", command=select_file).pack(pady=5)

        def submit_request():
            name = name_entry.get()
            contact = contact_entry.get()
            id_proof = id_proof_entry.get()
            reason = reason_entry.get()
            attachment = attachment_label.cget("text")
            if name and contact and id_proof and reason:
                success, message = self.system.register_request(name, contact, id_proof, reason, attachment)
                if success:
                    messagebox.showinfo("Success", message)
                    self.create_home_screen()
                else:
                    messagebox.showwarning("Duplicate Detected", message)
            else:
                messagebox.showerror("Error", "All fields except attachment are required!")

        tk.Button(self.root, text="Submit", command=submit_request).pack(pady=10)
        tk.Button(self.root, text="Back", command=self.create_home_screen).pack(pady=5)

    def create_status_screen(self):
        self.clear_window()

        tk.Label(self.root, text="Check Request Status", font=("Arial", 16, "bold")).pack(pady=10)

        tk.Label(self.root, text="Enter Request ID:").pack(pady=5)
        request_id_entry = tk.Entry(self.root)
        request_id_entry.pack()

        def check_status():
            try:
                request_id = int(request_id_entry.get())
                message = self.system.get_request_status(request_id)
                messagebox.showinfo("Status", message)
            except ValueError:
                messagebox.showerror("Error", "Invalid Request ID!")

        tk.Button(self.root, text="Check Status", command=check_status).pack(pady=10)
        tk.Button(self.root, text="Back", command=self.create_home_screen).pack(pady=5)

    def admin_login_screen(self):
        self.clear_window()

        tk.Label(self.root, text="Admin Login", font=("Arial", 16, "bold")).pack(pady=10)

        tk.Label(self.root, text="Username:").pack(pady=5)
        username_entry = tk.Entry(self.root)
        username_entry.pack()

        tk.Label(self.root, text="Password:").pack(pady=5)
        password_entry = tk.Entry(self.root, show="*")
        password_entry.pack()

        def login():
            username = username_entry.get()
            password = password_entry.get()
            if username in self.system.admin_credentials and self.system.admin_credentials[username] == password:
                self.create_admin_screen()
            else:
                messagebox.showerror("Error", "Invalid credentials!")

        tk.Button(self.root, text="Login", command=login).pack(pady=10)
        tk.Button(self.root, text="Back", command=self.create_home_screen).pack(pady=5)

    def create_admin_screen(self):
        self.clear_window()

        tk.Label(self.root, text="Admin Panel", font=("Arial", 16, "bold")).pack(pady=10)

        # Add AI stats at the top
        stats = self.system.get_duplicate_stats()
        stats_text = (f"AI Stats: Total Requests: {stats['total_requests']} | "
                      f"Potential Duplicates: {stats['potential_duplicates']} | "
                      f"Duplicate Rate: {stats['duplicate_percentage']:.1f}%")
        tk.Label(self.root, text=stats_text, font=("Arial", 10), fg="blue").pack(pady=5)

        # Create a frame with scrollbar for the requests
        frame = tk.Frame(self.root)
        frame.pack(fill="both", expand=True, padx=10, pady=5)

        # Add a scrollbar
        scrollbar = tk.Scrollbar(frame)
        scrollbar.pack(side="right", fill="y")

        # Create a canvas for scrolling
        canvas = tk.Canvas(frame, yscrollcommand=scrollbar.set)
        canvas.pack(side="left", fill="both", expand=True)

        scrollbar.config(command=canvas.yview)

        # Create a frame inside the canvas to hold the request items
        request_frame = tk.Frame(canvas)
        canvas.create_window((0, 0), window=request_frame, anchor="nw")

        if not self.system.requests:
            tk.Label(request_frame, text="No requests found.").pack()
        else:
            for request in self.system.requests:
                # Create a frame for each request
                req_frame = tk.Frame(request_frame, borderwidth=1, relief="solid", padx=5, pady=5)
                req_frame.pack(fill="x", expand=True, pady=5)

                # Add timestamp info if available
                timestamp_str = ""
                if "timestamp" in request:
                    try:
                        dt = datetime.datetime.fromisoformat(request["timestamp"])
                        timestamp_str = f" | Date: {dt.strftime('%Y-%m-%d %H:%M')}"
                    except:
                        pass

                details = (
                    f"ID: {request['request_id']} | Name: {request['name']} | Status: {request['status']}{timestamp_str}\n"
                    f"Reason: {request['reason']} | Attachment: {request['attachment']}"
                )
                tk.Label(req_frame, text=details, justify="left", wraplength=500).pack(anchor="w")

                if request["attachment"] and os.path.exists(request["attachment"]):
                    def open_file(filepath=request["attachment"]):
                        try:
                            os.startfile(filepath)
                        except Exception as e:
                            messagebox.showerror("Error", f"Could not open file: {e}")

                    tk.Button(req_frame, text="Open Attachment", command=open_file).pack(pady=2, anchor="w")

            # Update scroll region after adding all items
            request_frame.update_idletasks()
            canvas.config(scrollregion=canvas.bbox("all"))

        # Add controls at the bottom
        control_frame = tk.Frame(self.root)
        control_frame.pack(pady=10)

        tk.Label(control_frame, text="Enter Request ID to Process:").pack(side="left", padx=5)
        request_id_entry = tk.Entry(control_frame, width=10)
        request_id_entry.pack(side="left", padx=5)

        def approve():
            try:
                request_id = int(request_id_entry.get())
                message = self.system.approve_request(request_id)
                messagebox.showinfo("Result", message)
                self.create_admin_screen()
            except ValueError:
                messagebox.showerror("Error", "Invalid Request ID!")

        def deny():
            try:
                request_id = int(request_id_entry.get())
                message = self.system.deny_request(request_id)
                messagebox.showinfo("Result", message)
                self.create_admin_screen()
            except ValueError:
                messagebox.showerror("Error", "Invalid Request ID!")

        def delete():
            try:
                request_id = int(request_id_entry.get())
                if messagebox.askyesno("Confirm", f"Are you sure you want to delete request {request_id}?"):
                    message = self.system.delete_request(request_id)
                    messagebox.showinfo("Result", message)
                    self.create_admin_screen()
            except ValueError:
                messagebox.showerror("Error", "Invalid Request ID!")

        def delete_all():
            if messagebox.askyesno("Confirm", "Are you sure you want to delete ALL requests? This cannot be undone."):
                message = self.system.delete_all_requests()
                messagebox.showinfo("Result", message)
                self.create_admin_screen()

        button_frame = tk.Frame(self.root)
        button_frame.pack(pady=5)

        tk.Button(button_frame, text="Approve", command=approve).pack(side="left", padx=5)
        tk.Button(button_frame, text="Deny", command=deny).pack(side="left", padx=5)
        tk.Button(button_frame, text="Delete", command=delete).pack(side="left", padx=5)
        tk.Button(button_frame, text="Delete All", command=delete_all).pack(side="left", padx=5)
        tk.Button(button_frame, text="Back", command=self.create_home_screen).pack(side="left", padx=5)


if __name__ == "__main__":
    root = tk.Tk()
    app = EPassGUI(root)
    root.mainloop()