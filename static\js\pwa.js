// PWA functionality for E-Pass System
class PWAManager {
  constructor() {
    this.deferredPrompt = null;
    this.isInstalled = false;
    this.isOnline = navigator.onLine;
    this.offlineQueue = [];

    this.init();
  }

  init() {
    // Register service worker
    this.registerServiceWorker();

    // Setup install prompt
    this.setupInstallPrompt();

    // Setup offline/online detection
    this.setupNetworkDetection();

    // Setup push notifications
    this.setupPushNotifications();

    // Handle URL parameters for shortcuts
    this.handleShortcuts();

    // Setup offline form handling
    this.setupOfflineFormHandling();
  }

  // Register service worker
  async registerServiceWorker() {
    if ("serviceWorker" in navigator) {
      try {
        const registration = await navigator.serviceWorker.register(
          "/static/sw.js"
        );
        console.log(
          "PWA: Service Worker registered successfully",
          registration
        );

        // Handle service worker updates
        registration.addEventListener("updatefound", () => {
          const newWorker = registration.installing;
          newWorker.addEventListener("statechange", () => {
            if (
              newWorker.state === "installed" &&
              navigator.serviceWorker.controller
            ) {
              this.showUpdateAvailable();
            }
          });
        });
      } catch (error) {
        console.error("PWA: Service Worker registration failed", error);
      }
    }
  }

  // Setup install prompt
  setupInstallPrompt() {
    // Listen for beforeinstallprompt event
    window.addEventListener("beforeinstallprompt", (e) => {
      console.log("PWA: Install prompt available");
      e.preventDefault();
      this.deferredPrompt = e;
      this.showInstallButton();
    });

    // Listen for app installed event
    window.addEventListener("appinstalled", () => {
      console.log("PWA: App installed successfully");
      this.isInstalled = true;
      this.hideInstallButton();
      this.showToast(
        "App installed successfully! You can now access it from your home screen.",
        "success"
      );
    });

    // Check if already installed
    if (
      window.matchMedia("(display-mode: standalone)").matches ||
      window.navigator.standalone
    ) {
      this.isInstalled = true;
    }
  }

  // Show install button
  showInstallButton() {
    let installButton = document.getElementById("pwa-install-btn");

    if (!installButton) {
      installButton = document.createElement("button");
      installButton.id = "pwa-install-btn";
      installButton.className = "btn btn-primary pwa-install-btn";
      installButton.innerHTML = '<i class="fas fa-download"></i> Install App';
      installButton.onclick = () => this.promptInstall();

      // Add to navigation or hero section
      const navbar = document.querySelector(".navbar .nav-links");
      if (navbar) {
        navbar.appendChild(installButton);
      }
    }

    installButton.style.display = "inline-block";
  }

  // Hide install button
  hideInstallButton() {
    const installButton = document.getElementById("pwa-install-btn");
    if (installButton) {
      installButton.style.display = "none";
    }
  }

  // Prompt user to install
  async promptInstall() {
    if (!this.deferredPrompt) {
      this.showToast(
        "Installation not available on this device/browser.",
        "info"
      );
      return;
    }

    try {
      this.deferredPrompt.prompt();
      const { outcome } = await this.deferredPrompt.userChoice;

      if (outcome === "accepted") {
        console.log("PWA: User accepted install prompt");
      } else {
        console.log("PWA: User dismissed install prompt");
      }

      this.deferredPrompt = null;
    } catch (error) {
      console.error("PWA: Install prompt failed", error);
    }
  }

  // Setup network detection
  setupNetworkDetection() {
    window.addEventListener("online", () => {
      console.log("PWA: Back online");
      this.isOnline = true;
      this.hideOfflineIndicator();
      this.syncOfflineData();
      this.showToast("You are back online!", "success");
    });

    window.addEventListener("offline", () => {
      console.log("PWA: Gone offline");
      this.isOnline = false;
      this.showOfflineIndicator();
      this.showToast(
        "You are offline. Some features may be limited.",
        "warning"
      );
    });

    // Show offline indicator if already offline
    if (!this.isOnline) {
      this.showOfflineIndicator();
    }
  }

  // Show offline indicator
  showOfflineIndicator() {
    let indicator = document.getElementById("offline-indicator");

    if (!indicator) {
      indicator = document.createElement("div");
      indicator.id = "offline-indicator";
      indicator.className = "offline-indicator";
      indicator.innerHTML = '<i class="fas fa-wifi"></i> Offline Mode';
      document.body.appendChild(indicator);
    }

    indicator.style.display = "block";
  }

  // Hide offline indicator
  hideOfflineIndicator() {
    const indicator = document.getElementById("offline-indicator");
    if (indicator) {
      indicator.style.display = "none";
    }
  }

  // Setup push notifications
  async setupPushNotifications() {
    if ("Notification" in window && "serviceWorker" in navigator) {
      // Request notification permission
      if (Notification.permission === "default") {
        const permission = await Notification.requestPermission();
        console.log("PWA: Notification permission:", permission);
      }
    }
  }

  // Handle shortcuts from manifest
  handleShortcuts() {
    const urlParams = new URLSearchParams(window.location.search);
    const action = urlParams.get("action");

    if (action) {
      setTimeout(() => {
        switch (action) {
          case "request":
            if (typeof showRequestForm === "function") showRequestForm();
            break;
          case "status":
            if (typeof showStatusCheck === "function") showStatusCheck();
            break;
          case "admin":
            if (typeof showAdminLogin === "function") showAdminLogin();
            break;
        }
      }, 500);
    }
  }

  // Setup offline form handling
  setupOfflineFormHandling() {
    // Intercept form submissions when offline
    document.addEventListener("submit", (e) => {
      if (!this.isOnline && e.target.tagName === "FORM") {
        e.preventDefault();
        this.handleOfflineSubmission(e.target);
      }
    });
  }

  // Handle offline form submission
  handleOfflineSubmission(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Store in offline queue
    this.offlineQueue.push({
      url: form.action || window.location.pathname,
      method: form.method || "POST",
      data: data,
      timestamp: Date.now(),
    });

    // Store in localStorage for persistence
    localStorage.setItem(
      "pwa-offline-queue",
      JSON.stringify(this.offlineQueue)
    );

    this.showToast(
      "Request saved offline. It will be submitted when you're back online.",
      "info"
    );
  }

  // Sync offline data when back online
  async syncOfflineData() {
    const storedQueue = localStorage.getItem("pwa-offline-queue");
    if (storedQueue) {
      this.offlineQueue = JSON.parse(storedQueue);
    }

    if (this.offlineQueue.length === 0) return;

    console.log("PWA: Syncing offline data...");

    for (let i = this.offlineQueue.length - 1; i >= 0; i--) {
      const request = this.offlineQueue[i];

      try {
        const response = await fetch(request.url, {
          method: request.method,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(request.data),
        });

        if (response.ok) {
          this.offlineQueue.splice(i, 1);
          console.log("PWA: Offline request synced successfully");
        }
      } catch (error) {
        console.error("PWA: Failed to sync offline request", error);
      }
    }

    // Update localStorage
    localStorage.setItem(
      "pwa-offline-queue",
      JSON.stringify(this.offlineQueue)
    );

    if (this.offlineQueue.length === 0) {
      this.showToast("All offline requests have been synced!", "success");
    }
  }

  // Show update available notification
  showUpdateAvailable() {
    const updateBanner = document.createElement("div");
    updateBanner.className = "update-banner";
    updateBanner.innerHTML = `
      <div class="update-content">
        <span>A new version is available!</span>
        <button onclick="pwaManager.applyUpdate()" class="btn btn-sm btn-primary">Update</button>
        <button onclick="this.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">Later</button>
      </div>
    `;
    document.body.appendChild(updateBanner);
  }

  // Apply update
  applyUpdate() {
    if ("serviceWorker" in navigator) {
      navigator.serviceWorker.getRegistration().then((registration) => {
        if (registration && registration.waiting) {
          registration.waiting.postMessage({ type: "SKIP_WAITING" });
          window.location.reload();
        }
      });
    }
  }

  // Utility method to show toast (reuse existing function if available)
  showToast(message, type) {
    if (typeof showToast === "function") {
      showToast(message, type);
    } else {
      console.log(`PWA Toast [${type}]: ${message}`);
    }
  }
}

// Initialize PWA when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
  window.pwaManager = new PWAManager();
});
