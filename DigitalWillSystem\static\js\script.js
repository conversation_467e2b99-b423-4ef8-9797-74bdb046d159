// Digital Will System JavaScript

// Global variables
let currentUser = null;

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  // Check for saved user session
  const savedUser = localStorage.getItem("digitalWillUser");
  if (savedUser) {
    currentUser = JSON.parse(savedUser);
    updateNavigation();
  }

  // Add event listeners
  const loginForm = document.getElementById("login-form");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  const registerForm = document.getElementById("register-form");
  if (registerForm) {
    registerForm.addEventListener("submit", handleRegister);
  }

  // Initialize PWA
  initializePWA();
});

// Screen navigation functions
function showScreen(screenId) {
  const screens = document.querySelectorAll(".screen");
  screens.forEach((screen) => screen.classList.remove("active"));

  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add("active");
  }
}

function showHome() {
  showScreen("home-screen");
}

function showLogin() {
  showScreen("login-screen");
}

function showRegister() {
  showScreen("register-screen");
}

function showDashboard() {
  showScreen("dashboard-screen");
  if (currentUser) {
    loadDashboardData();
  }
}

function showAdminLogin() {
  showScreen("admin-login-screen");
}

function updateNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  
  if (currentUser) {
    // Update navigation for logged-in users
    navLinks.forEach(link => {
      if (link.textContent.includes("Login")) {
        link.innerHTML = '<i class="fas fa-tachometer-alt"></i> Dashboard';
        link.onclick = () => showDashboard();
      }
    });
  }
}

// Authentication functions
async function handleLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    phone_number: formData.get("phone_number"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.user;
      localStorage.setItem("digitalWillUser", JSON.stringify(currentUser));
      showToast("Login successful!", "success");
      updateNavigation();
      showDashboard();
      // Clear form
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Login failed. Please try again.", "error");
  }
}

async function handleRegister(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const registerData = {
    full_name: formData.get("full_name"),
    phone_number: formData.get("phone_number"),
    email: formData.get("email"),
    national_id: formData.get("national_id"),
    date_of_birth: formData.get("date_of_birth"),
    address: formData.get("address"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/register", "POST", registerData);
    hideLoading();

    if (result.success) {
      showToast("Registration successful! Please login.", "success");
      showLogin();
      // Clear form
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Registration failed. Please try again.", "error");
  }
}

function logout() {
  currentUser = null;
  localStorage.removeItem("digitalWillUser");
  showToast("Logged out successfully!", "success");
  showHome();
  updateNavigation();
}

// Dashboard functions
async function loadDashboardData() {
  if (!currentUser) return;

  try {
    // Load user's wills
    const willsResult = await apiCall(`/user/wills/${currentUser.id}`, "GET");
    if (willsResult.success) {
      updateWillsList(willsResult.wills);
    }
  } catch (error) {
    showToast("Failed to load dashboard data", "error");
  }
}

function updateWillsList(wills) {
  // This function would update the dashboard with user's wills
  console.log("User wills:", wills);
}

// Will creation functions
function showCreateWill() {
  showScreen("create-will-screen");
}

function showWillDetails(willId) {
  showScreen("will-details-screen");
  loadWillDetails(willId);
}

async function loadWillDetails(willId) {
  try {
    // Load will details
    showToast("Loading will details...", "info");
  } catch (error) {
    showToast("Failed to load will details", "error");
  }
}

// Utility functions
function showLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.add("active");
  }
}

function hideLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.remove("active");
  }
}

function showToast(message, type = "info") {
  const toastContainer = document.getElementById("toast-container");
  if (!toastContainer) return;

  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.textContent = message;

  toastContainer.appendChild(toast);

  // Remove toast after 5 seconds
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 5000);
}

// API helper function
async function apiCall(endpoint, method = "GET", data = null) {
  const url = `/api${endpoint}`;
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data && method !== "GET") {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  return await response.json();
}

// PWA functions
function initializePWA() {
  // Register service worker
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("SW registered: ", registration);
        })
        .catch((registrationError) => {
          console.log("SW registration failed: ", registrationError);
        });
    });
  }

  // Handle install prompt
  let deferredPrompt;
  window.addEventListener("beforeinstallprompt", (e) => {
    e.preventDefault();
    deferredPrompt = e;
    showInstallButton();
  });

  function showInstallButton() {
    // Show install button if needed
    console.log("PWA can be installed");
  }
}

// Legal validation functions
async function requestLegalValidation(willId) {
  try {
    showLoading();
    
    // Get available validators
    const validatorsResult = await apiCall("/legal-validators", "GET");
    
    if (validatorsResult.success) {
      hideLoading();
      showValidatorSelection(willId, validatorsResult.validators);
    } else {
      hideLoading();
      showToast("Failed to load validators", "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Failed to request validation", "error");
  }
}

function showValidatorSelection(willId, validators) {
  // This would show a modal or screen to select a validator
  console.log("Available validators:", validators);
  showToast("Validator selection feature coming soon!", "info");
}

// Digital witness functions
function addDigitalWitness(willId) {
  showToast("Digital witness feature coming soon!", "info");
}

function verifyWitness(verificationCode) {
  showToast("Witness verification feature coming soon!", "info");
}

// Payment functions
function processPayment(amount, paymentType) {
  showToast("Payment processing feature coming soon!", "info");
}

// Admin functions
function showAdminDashboard() {
  showToast("Admin dashboard feature coming soon!", "info");
}
