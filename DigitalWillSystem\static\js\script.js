// Digital Will System JavaScript

// Global variables
let currentUser = null;
let currentLanguage = "en";

// Language translations
const translations = {
  en: {
    // Navigation
    home: "Home",
    login: "Login",
    register: "Register",
    admin: "Admin",

    // System
    system_title: "Digital Will & Inheritance System",
    hero_subtitle: "Secure, Legal, and Transparent Digital Wills for Rwanda",
    hero_description:
      "Create legally binding digital wills with digital witnesses and legal validation. Prevent inheritance disputes and ensure your legacy is protected.",

    // Hero Stats
    secure: "100% Secure",
    encrypted_storage: "Encrypted Storage",
    legal_validation: "Legal Validation",
    certified_lawyers: "Certified Lawyers",
    digital_witnesses: "Digital Witnesses",
    verified_identity: "Verified Identity",
    access_247: "24/7 Access",
    anytime_anywhere: "Anytime, Anywhere",

    // Action Cards
    create_will: "Create Your Will",
    create_will_desc: "Secure digital will creation with legal templates",
    new_user_registration: "New User Registration",
    new_user_desc: "Join thousands protecting their legacy",
    legal_validator_portal: "Legal Validator Portal",
    legal_validator_desc: "For certified lawyers and legal professionals",
    free_registration: "Free Registration",
    professional_access: "Professional Access",

    // Features
    why_choose: "Why Choose Digital Will System?",
    secure_encrypted: "Secure & Encrypted",
    secure_encrypted_desc: "Bank-level security with end-to-end encryption",
    legal_compliance: "Legal Compliance",
    legal_compliance_desc: "Compliant with Rwandan inheritance laws",
    privacy_protected: "Privacy Protected",
    privacy_protected_desc: "Your will remains private until needed",
    quick_process: "Quick Process",
    quick_process_desc: "Create your will in under 30 minutes",
    digital_witnesses_feature: "Digital Witnesses",
    digital_witnesses_desc: "Verified digital witnesses for authenticity",
    legal_validation_feature: "Legal Validation",
    legal_validation_desc: "Optional validation by certified lawyers",

    // Forms
    user_login: "User Login",
    user_login_desc: "Access your digital will dashboard",
    phone_number: "Phone Number",
    enter_phone: "Enter your phone number",
    password: "Password",
    enter_password: "Enter your password",
    back: "Back",
    dont_have_account: "Don't have an account?",
    register_here: "Register here",

    user_registration: "User Registration",
    user_registration_desc: "Create your secure digital will account",
    full_name: "Full Name",
    enter_full_name: "Enter your full name",
    national_id: "National ID",
    enter_national_id: "Enter your National ID",
    enter_phone_number: "Enter your phone number",
    email_address: "Email Address",
    enter_email: "Enter your email",
    date_of_birth: "Date of Birth",
    create_password: "Create a strong password",
    address: "Address",
    enter_address: "Enter your full address",
    already_have_account: "Already have an account?",
    login_here: "Login here",

    // Admin
    admin_login: "Admin Login",
    admin_login_desc: "Access the administrative dashboard",
    username: "Username",
    admin_dashboard: "Admin Dashboard",
    welcome_admin: "Welcome, Administrator!",
    logout: "Logout",
    total_users: "Total Users",
    total_wills: "Total Wills",
    monthly_revenue: "Monthly Revenue (RWF)",
    pending_validations: "Pending Validations",
    users: "Users",
    wills: "Wills",
    validators: "Validators",
    payments: "Payments",
    processing: "Processing...",
  },
  fr: {
    // Navigation
    home: "Accueil",
    login: "Connexion",
    register: "S'inscrire",
    admin: "Admin",

    // System
    system_title: "Système de Testament et Héritage Numérique",
    hero_subtitle:
      "Testaments Numériques Sécurisés, Légaux et Transparents pour le Rwanda",
    hero_description:
      "Créez des testaments numériques juridiquement contraignants avec des témoins numériques et une validation légale. Prévenez les disputes d'héritage et protégez votre héritage.",

    // Hero Stats
    secure: "100% Sécurisé",
    encrypted_storage: "Stockage Crypté",
    legal_validation: "Validation Légale",
    certified_lawyers: "Avocats Certifiés",
    digital_witnesses: "Témoins Numériques",
    verified_identity: "Identité Vérifiée",
    access_247: "Accès 24/7",
    anytime_anywhere: "N'importe quand, N'importe où",

    // Action Cards
    create_will: "Créez Votre Testament",
    create_will_desc:
      "Création sécurisée de testament numérique avec modèles légaux",
    new_user_registration: "Inscription Nouvel Utilisateur",
    new_user_desc: "Rejoignez des milliers protégeant leur héritage",
    legal_validator_portal: "Portail Validateur Légal",
    legal_validator_desc:
      "Pour les avocats certifiés et professionnels juridiques",
    free_registration: "Inscription Gratuite",
    professional_access: "Accès Professionnel",

    // Features
    why_choose: "Pourquoi Choisir le Système de Testament Numérique?",
    secure_encrypted: "Sécurisé et Crypté",
    secure_encrypted_desc:
      "Sécurité de niveau bancaire avec cryptage de bout en bout",
    legal_compliance: "Conformité Légale",
    legal_compliance_desc: "Conforme aux lois d'héritage rwandaises",
    privacy_protected: "Confidentialité Protégée",
    privacy_protected_desc:
      "Votre testament reste privé jusqu'à ce qu'il soit nécessaire",
    quick_process: "Processus Rapide",
    quick_process_desc: "Créez votre testament en moins de 30 minutes",
    digital_witnesses_feature: "Témoins Numériques",
    digital_witnesses_desc: "Témoins numériques vérifiés pour l'authenticité",
    legal_validation_feature: "Validation Légale",
    legal_validation_desc: "Validation optionnelle par des avocats certifiés",

    // Forms
    user_login: "Connexion Utilisateur",
    user_login_desc: "Accédez à votre tableau de bord de testament numérique",
    phone_number: "Numéro de Téléphone",
    enter_phone: "Entrez votre numéro de téléphone",
    password: "Mot de Passe",
    enter_password: "Entrez votre mot de passe",
    back: "Retour",
    dont_have_account: "Vous n'avez pas de compte?",
    register_here: "Inscrivez-vous ici",

    user_registration: "Inscription Utilisateur",
    user_registration_desc:
      "Créez votre compte de testament numérique sécurisé",
    full_name: "Nom Complet",
    enter_full_name: "Entrez votre nom complet",
    national_id: "Carte d'Identité Nationale",
    enter_national_id: "Entrez votre carte d'identité nationale",
    enter_phone_number: "Entrez votre numéro de téléphone",
    email_address: "Adresse Email",
    enter_email: "Entrez votre email",
    date_of_birth: "Date de Naissance",
    create_password: "Créez un mot de passe fort",
    address: "Adresse",
    enter_address: "Entrez votre adresse complète",
    already_have_account: "Vous avez déjà un compte?",
    login_here: "Connectez-vous ici",

    // Admin
    admin_login: "Connexion Administrateur",
    admin_login_desc: "Accéder au tableau de bord administratif",
    username: "Nom d'utilisateur",
    admin_dashboard: "Tableau de Bord Administrateur",
    welcome_admin: "Bienvenue, Administrateur!",
    logout: "Se déconnecter",
    total_users: "Total Utilisateurs",
    total_wills: "Total Testaments",
    monthly_revenue: "Revenus Mensuels (RWF)",
    pending_validations: "Validations en Attente",
    users: "Utilisateurs",
    wills: "Testaments",
    validators: "Validateurs",
    payments: "Paiements",
    processing: "Traitement en cours...",
  },
  rw: {
    // Navigation
    home: "Ahabanza",
    login: "Injira",
    register: "Iyandikishe",
    admin: "Umuyobozi",

    // System
    system_title: "Sisitemu y'Umurage n'Indangagaciro za Digitale",
    hero_subtitle:
      "Indangagaciro z'Umurage Zitekanye, z'Amategeko kandi Zisobanutse mu Rwanda",
    hero_description:
      "Kora indangagaciro z'umurage zifite uburemere bw'amategeko hamwe n'abatangabuhamya ba digitale n'iyemeza ry'amategeko. Wirinde amakimbirane y'umurage kandi urinde umurage wawe.",

    // Hero Stats
    secure: "100% Byizewe",
    encrypted_storage: "Ububiko Bwihishe",
    legal_validation: "Iyemeza ry'Amategeko",
    certified_lawyers: "Abavoka Bemejwe",
    digital_witnesses: "Abatangabuhamya ba Digitale",
    verified_identity: "Indangamuntu Yemejwe",
    access_247: "Kubona 24/7",
    anytime_anywhere: "Igihe cyose, Ahantu hose",

    // Action Cards
    create_will: "Kora Indangagaciro Yawe",
    create_will_desc:
      "Gukora indangagaciro y'umurage itekanye hamwe n'inyandikomvugo z'amategeko",
    new_user_registration: "Kwiyandikisha kw'Ukoresha Mushya",
    new_user_desc: "Jya mu bihumbi birinda umurage wabo",
    legal_validator_portal: "Urubuga rw'Abemeza b'Amategeko",
    legal_validator_desc: "Ku bavoka bemejwe n'abanyamwuga b'amategeko",
    free_registration: "Kwiyandikisha Ubuntu",
    professional_access: "Kubona kw'Abanyamwuga",

    // Features
    why_choose: "Kuki Uhitamo Sisitemu y'Indangagaciro za Digitale?",
    secure_encrypted: "Byizewe kandi Byihishe",
    secure_encrypted_desc:
      "Umutekano w'urwego rw'amabanki hamwe n'ubwihisho bwuzuye",
    legal_compliance: "Kubahiriza Amategeko",
    legal_compliance_desc: "Bihuza n'amategeko y'umurage yo mu Rwanda",
    privacy_protected: "Ibanga Ryarinzwe",
    privacy_protected_desc:
      "Indangagaciro yawe iguma ari iy'ibanga kugeza igihe ikenewe",
    quick_process: "Inzira Yihuse",
    quick_process_desc: "Kora indangagaciro yawe mu minota itarenze 30",
    digital_witnesses_feature: "Abatangabuhamya ba Digitale",
    digital_witnesses_desc: "Abatangabuhamya ba digitale bemejwe ku kuri",
    legal_validation_feature: "Iyemeza ry'Amategeko",
    legal_validation_desc: "Iyemeza ritari ngombwa ryakozwe n'abavoka bemejwe",

    // Forms
    user_login: "Kwinjira kw'Ukoresha",
    user_login_desc: "Injira mu kibaho cyawe cy'indangagaciro za digitale",
    phone_number: "Nimero ya Telefoni",
    enter_phone: "Injiza nimero ya telefoni yawe",
    password: "Ijambo ry'Ibanga",
    enter_password: "Injiza ijambo ryawe ry'ibanga",
    back: "Subira",
    dont_have_account: "Ntufite konti?",
    register_here: "Iyandikishe hano",

    user_registration: "Kwiyandikisha kw'Ukoresha",
    user_registration_desc:
      "Kora konti yawe itekanye y'indangagaciro za digitale",
    full_name: "Amazina Yuzuye",
    enter_full_name: "Injiza amazina yawe yuzuye",
    national_id: "Indangamuntu",
    enter_national_id: "Injiza indangamuntu yawe",
    enter_phone_number: "Injiza nimero ya telefoni yawe",
    email_address: "Aderesi ya Email",
    enter_email: "Injiza email yawe",
    date_of_birth: "Italiki y'Amavuko",
    create_password: "Kora ijambo ry'ibanga rikomeye",
    address: "Aderesi",
    enter_address: "Injiza aderesi yawe yuzuye",
    already_have_account: "Usanzwe ufite konti?",
    login_here: "Injira hano",

    // Admin
    admin_login: "Kwinjira kw'Umuyobozi",
    admin_login_desc: "Injira mu kibaho cy'ubuyobozi",
    username: "Izina ry'Ukoresha",
    admin_dashboard: "Ikibaho cy'Umuyobozi",
    welcome_admin: "Murakaza neza, Umuyobozi!",
    logout: "Sohoka",
    total_users: "Abakoresha Bose",
    total_wills: "Indangagaciro Zose",
    monthly_revenue: "Amafaranga y'Ukwezi (RWF)",
    pending_validations: "Iyemeza Ritegereje",
    users: "Abakoresha",
    wills: "Indangagaciro",
    validators: "Abemeza",
    payments: "Kwishyura",
    processing: "Gutegura...",
  },
};

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  // Check for saved user session
  const savedUser = localStorage.getItem("digitalWillUser");
  if (savedUser) {
    currentUser = JSON.parse(savedUser);
    updateNavigation();
  }

  // Add event listeners
  const loginForm = document.getElementById("login-form");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  const registerForm = document.getElementById("register-form");
  if (registerForm) {
    registerForm.addEventListener("submit", handleRegister);
  }

  const adminLoginForm = document.getElementById("admin-login-form");
  if (adminLoginForm) {
    adminLoginForm.addEventListener("submit", handleAdminLogin);
  }

  // Load saved language
  const savedLanguage = localStorage.getItem("digitalWillLanguage");
  if (savedLanguage) {
    currentLanguage = savedLanguage;
    updateLanguage();
  }

  // Initialize PWA
  initializePWA();
});

// Screen navigation functions
function showScreen(screenId) {
  const screens = document.querySelectorAll(".screen");
  screens.forEach((screen) => screen.classList.remove("active"));

  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add("active");
  }
}

function showHome() {
  showScreen("home-screen");
}

function showLogin() {
  showScreen("login-screen");
}

function showRegister() {
  showScreen("register-screen");
}

function showDashboard() {
  showScreen("dashboard-screen");
  if (currentUser) {
    loadDashboardData();
  }
}

function showAdminLogin() {
  showScreen("admin-login-screen");
}

function showAdminDashboard() {
  showScreen("admin-dashboard-screen");
  if (currentUser && currentUser.username) {
    loadAdminDashboard();
  }
}

function updateNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");

  if (currentUser) {
    // Update navigation for logged-in users
    navLinks.forEach((link) => {
      if (link.textContent.includes("Login")) {
        link.innerHTML = '<i class="fas fa-tachometer-alt"></i> Dashboard';
        link.onclick = () => showDashboard();
      }
    });
  }
}

// Authentication functions
async function handleLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    phone_number: formData.get("phone_number"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.user;
      localStorage.setItem("digitalWillUser", JSON.stringify(currentUser));
      showToast("Login successful!", "success");
      updateNavigation();
      showDashboard();
      // Clear form
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Login failed. Please try again.", "error");
  }
}

async function handleRegister(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const registerData = {
    full_name: formData.get("full_name"),
    phone_number: formData.get("phone_number"),
    email: formData.get("email"),
    national_id: formData.get("national_id"),
    date_of_birth: formData.get("date_of_birth"),
    address: formData.get("address"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/register", "POST", registerData);
    hideLoading();

    if (result.success) {
      showToast("Registration successful! Please login.", "success");
      showLogin();
      // Clear form
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Registration failed. Please try again.", "error");
  }
}

function logout() {
  currentUser = null;
  localStorage.removeItem("digitalWillUser");
  showToast("Logged out successfully!", "success");
  showHome();
  updateNavigation();
}

// Dashboard functions
async function loadDashboardData() {
  if (!currentUser) return;

  try {
    // Load user's wills
    const willsResult = await apiCall(`/user/wills/${currentUser.id}`, "GET");
    if (willsResult.success) {
      updateWillsList(willsResult.wills);
    }
  } catch (error) {
    showToast("Failed to load dashboard data", "error");
  }
}

function updateWillsList(wills) {
  // This function would update the dashboard with user's wills
  console.log("User wills:", wills);
}

// Will creation functions
function showCreateWill() {
  showScreen("create-will-screen");
}

function showWillDetails(willId) {
  showScreen("will-details-screen");
  loadWillDetails(willId);
}

async function loadWillDetails(willId) {
  try {
    // Load will details
    showToast("Loading will details...", "info");
  } catch (error) {
    showToast("Failed to load will details", "error");
  }
}

// Utility functions
function showLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.add("active");
  }
}

function hideLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.remove("active");
  }
}

function showToast(message, type = "info") {
  const toastContainer = document.getElementById("toast-container");
  if (!toastContainer) return;

  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.textContent = message;

  toastContainer.appendChild(toast);

  // Remove toast after 5 seconds
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 5000);
}

// API helper function
async function apiCall(endpoint, method = "GET", data = null) {
  const url = `/api${endpoint}`;
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data && method !== "GET") {
    options.body = JSON.stringify(data);
  }

  const response = await fetch(url, options);
  return await response.json();
}

// PWA functions
function initializePWA() {
  // Register service worker
  if ("serviceWorker" in navigator) {
    window.addEventListener("load", () => {
      navigator.serviceWorker
        .register("/sw.js")
        .then((registration) => {
          console.log("SW registered: ", registration);
        })
        .catch((registrationError) => {
          console.log("SW registration failed: ", registrationError);
        });
    });
  }

  // Handle install prompt
  let deferredPrompt;
  window.addEventListener("beforeinstallprompt", (e) => {
    e.preventDefault();
    deferredPrompt = e;
    showInstallButton();
  });

  function showInstallButton() {
    // Show install button if needed
    console.log("PWA can be installed");
  }
}

// Table generation functions
function generateUsersTable(users) {
  if (users.length === 0) {
    return '<div class="alert alert-info">No users found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Phone</th>
          <th>Email</th>
          <th>National ID</th>
          <th>Wills</th>
          <th>Status</th>
          <th>Joined</th>
        </tr>
      </thead>
      <tbody>
  `;

  users.forEach((user) => {
    html += `
      <tr>
        <td>${user.full_name}</td>
        <td>${user.phone_number}</td>
        <td>${user.email || "N/A"}</td>
        <td>${user.national_id}</td>
        <td>${user.will_count}</td>
        <td><span class="status-badge ${
          user.is_active ? "status-active" : "status-pending"
        }">${user.is_active ? "Active" : "Inactive"}</span></td>
        <td>${new Date(user.created_at).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generateWillsTable(wills) {
  if (wills.length === 0) {
    return '<div class="alert alert-info">No wills found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Will ID</th>
          <th>Title</th>
          <th>Owner</th>
          <th>Status</th>
          <th>Witnesses</th>
          <th>Created</th>
        </tr>
      </thead>
      <tbody>
  `;

  wills.forEach((will) => {
    html += `
      <tr>
        <td>${will.will_id}</td>
        <td>${will.title}</td>
        <td>${will.owner_name}</td>
        <td><span class="status-badge status-${will.status}">${
      will.status
    }</span></td>
        <td>${will.witness_count}</td>
        <td>${new Date(will.created_at).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generateValidatorsTable(validators) {
  if (validators.length === 0) {
    return '<div class="alert alert-info">No validators found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>License</th>
          <th>Specialization</th>
          <th>Email</th>
          <th>Phone</th>
        </tr>
      </thead>
      <tbody>
  `;

  validators.forEach((validator) => {
    html += `
      <tr>
        <td>${validator.name}</td>
        <td>${validator.license}</td>
        <td>${validator.specialization}</td>
        <td>${validator.email}</td>
        <td>${validator.phone}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generatePaymentsTable(payments) {
  if (payments.length === 0) {
    return '<div class="alert alert-info">No payments found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>User</th>
          <th>Type</th>
          <th>Amount</th>
          <th>Method</th>
          <th>Status</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
  `;

  payments.forEach((payment) => {
    html += `
      <tr>
        <td>${payment.user_name}</td>
        <td>${payment.payment_type}</td>
        <td>${payment.amount.toLocaleString()} RWF</td>
        <td>${payment.payment_method}</td>
        <td><span class="status-badge ${
          payment.payment_status === "completed"
            ? "status-active"
            : "status-pending"
        }">${payment.payment_status}</span></td>
        <td>${new Date(payment.payment_date).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

// Legal validation functions
async function requestLegalValidation(willId) {
  try {
    showLoading();

    // Get available validators
    const validatorsResult = await apiCall("/legal-validators", "GET");

    if (validatorsResult.success) {
      hideLoading();
      showValidatorSelection(willId, validatorsResult.validators);
    } else {
      hideLoading();
      showToast("Failed to load validators", "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Failed to request validation", "error");
  }
}

function showValidatorSelection(willId, validators) {
  // This would show a modal or screen to select a validator
  console.log("Available validators:", validators);
  showToast("Validator selection feature coming soon!", "info");
}

// Digital witness functions
function addDigitalWitness(willId) {
  showToast("Digital witness feature coming soon!", "info");
}

function verifyWitness(verificationCode) {
  showToast("Witness verification feature coming soon!", "info");
}

// Payment functions
function processPayment(amount, paymentType) {
  showToast("Payment processing feature coming soon!", "info");
}

// Admin functions
async function handleAdminLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    username: formData.get("username"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/admin/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.admin;
      currentUser.user_type = "admin";
      localStorage.setItem("digitalWillUser", JSON.stringify(currentUser));
      showToast("Admin login successful!", "success");
      showAdminDashboard();
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Admin login failed. Please try again.", "error");
  }
}

async function loadAdminDashboard() {
  try {
    // Load admin statistics
    const statsResult = await apiCall("/admin/stats", "GET");
    if (statsResult.success) {
      updateAdminStats(statsResult.stats);
    }

    // Load users by default
    showAdminTab("users");
  } catch (error) {
    showToast("Failed to load admin dashboard", "error");
  }
}

function updateAdminStats(stats) {
  const totalUsersElement = document.getElementById("total-users");
  const totalWillsElement = document.getElementById("total-wills");
  const monthlyRevenueElement = document.getElementById("monthly-revenue");
  const pendingValidationsElement = document.getElementById(
    "pending-validations"
  );

  if (totalUsersElement) totalUsersElement.textContent = stats.total_users || 0;
  if (totalWillsElement) totalWillsElement.textContent = stats.total_wills || 0;
  if (monthlyRevenueElement)
    monthlyRevenueElement.textContent = (
      stats.monthly_revenue || 0
    ).toLocaleString();
  if (pendingValidationsElement)
    pendingValidationsElement.textContent = stats.pending_validations || 0;
}

async function showAdminTab(tabName) {
  // Update tab buttons
  const tabButtons = document.querySelectorAll(".tab-btn");
  tabButtons.forEach((btn) => btn.classList.remove("active"));

  const activeTab = document.querySelector(
    `[onclick="showAdminTab('${tabName}')"]`
  );
  if (activeTab) activeTab.classList.add("active");

  // Load content based on tab
  const contentDiv = document.getElementById("admin-content");
  if (!contentDiv) return;

  contentDiv.innerHTML =
    '<div class="loading-table"><i class="fas fa-spinner fa-spin"></i><p>Loading...</p></div>';

  try {
    let result;
    switch (tabName) {
      case "users":
        result = await apiCall("/admin/users", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateUsersTable(result.users);
        }
        break;
      case "wills":
        result = await apiCall("/admin/wills", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateWillsTable(result.wills);
        }
        break;
      case "validators":
        result = await apiCall("/legal-validators", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateValidatorsTable(result.validators);
        }
        break;
      case "payments":
        result = await apiCall("/admin/payments", "GET");
        if (result.success) {
          contentDiv.innerHTML = generatePaymentsTable(result.payments);
        }
        break;
    }
  } catch (error) {
    contentDiv.innerHTML =
      '<div class="alert alert-error">Failed to load data</div>';
  }
}

// Language functions
function changeLanguage(lang) {
  currentLanguage = lang;
  localStorage.setItem("digitalWillLanguage", lang);
  updateLanguage();
  updateLanguageButtons();
  showToast(`Language changed to ${lang.toUpperCase()}`, "success");
}

function updateLanguage() {
  // Update text content
  const elements = document.querySelectorAll("[data-translate]");
  elements.forEach((element) => {
    const key = element.getAttribute("data-translate");
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      element.textContent = translations[currentLanguage][key];
    }
  });

  // Update placeholders
  const placeholderElements = document.querySelectorAll(
    "[data-translate-placeholder]"
  );
  placeholderElements.forEach((element) => {
    const key = element.getAttribute("data-translate-placeholder");
    if (translations[currentLanguage] && translations[currentLanguage][key]) {
      element.placeholder = translations[currentLanguage][key];
    }
  });
}

function updateLanguageButtons() {
  const languageButtons = document.querySelectorAll(".language-btn");
  languageButtons.forEach((btn) => {
    btn.classList.remove("active");
    if (btn.textContent.includes(currentLanguage.toUpperCase())) {
      btn.classList.add("active");
    }
  });
}
