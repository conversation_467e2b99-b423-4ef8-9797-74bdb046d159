<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Offline - E-Pass System</title>
    
    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#4f46e5" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    
    <!-- Stylesheets -->
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/pwa.css') }}"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    
    <style>
      .offline-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        text-align: center;
        padding: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      .offline-icon {
        font-size: 4rem;
        margin-bottom: 2rem;
        opacity: 0.8;
      }
      
      .offline-title {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        font-weight: bold;
      }
      
      .offline-message {
        font-size: 1.2rem;
        margin-bottom: 2rem;
        opacity: 0.9;
        max-width: 600px;
        line-height: 1.6;
      }
      
      .offline-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        justify-content: center;
      }
      
      .offline-btn {
        padding: 1rem 2rem;
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;
        text-decoration: none;
        border-radius: 50px;
        font-weight: bold;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }
      
      .offline-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
      }
      
      .connection-status {
        margin-top: 2rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        backdrop-filter: blur(10px);
      }
      
      .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #ff6b6b;
        margin-right: 0.5rem;
        animation: pulse 2s infinite;
      }
      
      .status-indicator.online {
        background: #51cf66;
      }
      
      @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
      }
      
      @media (max-width: 768px) {
        .offline-title {
          font-size: 2rem;
        }
        
        .offline-message {
          font-size: 1rem;
        }
        
        .offline-actions {
          flex-direction: column;
          width: 100%;
        }
        
        .offline-btn {
          width: 100%;
          max-width: 300px;
        }
      }
    </style>
  </head>
  <body>
    <div class="offline-container">
      <div class="offline-icon">
        <i class="fas fa-wifi" id="wifi-icon"></i>
      </div>
      
      <h1 class="offline-title">You're Offline</h1>
      
      <p class="offline-message">
        It looks like you've lost your internet connection. Don't worry - you can still view your previously loaded content, and any forms you submit will be saved and sent when you're back online.
      </p>
      
      <div class="offline-actions">
        <a href="/" class="offline-btn">
          <i class="fas fa-home"></i> Go to Home
        </a>
        <button onclick="window.location.reload()" class="offline-btn">
          <i class="fas fa-sync-alt"></i> Try Again
        </button>
      </div>
      
      <div class="connection-status">
        <span class="status-indicator" id="status-indicator"></span>
        <span id="status-text">Checking connection...</span>
      </div>
    </div>

    <script>
      // Check connection status
      function updateConnectionStatus() {
        const indicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        const wifiIcon = document.getElementById('wifi-icon');
        
        if (navigator.onLine) {
          indicator.classList.add('online');
          statusText.textContent = 'Connection restored! Redirecting...';
          wifiIcon.className = 'fas fa-wifi';
          
          // Redirect to home page after a short delay
          setTimeout(() => {
            window.location.href = '/';
          }, 2000);
        } else {
          indicator.classList.remove('online');
          statusText.textContent = 'No internet connection';
          wifiIcon.className = 'fas fa-wifi';
          wifiIcon.style.opacity = '0.5';
        }
      }
      
      // Check connection status on load
      updateConnectionStatus();
      
      // Listen for connection changes
      window.addEventListener('online', updateConnectionStatus);
      window.addEventListener('offline', updateConnectionStatus);
      
      // Check connection status every 5 seconds
      setInterval(updateConnectionStatus, 5000);
      
      // Add some visual feedback for button clicks
      document.querySelectorAll('.offline-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          this.style.transform = 'scale(0.95)';
          setTimeout(() => {
            this.style.transform = '';
          }, 150);
        });
      });
    </script>
  </body>
</html>
