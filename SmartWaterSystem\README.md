# Smart Water Distribution & Billing System

A comprehensive IoT-enabled water management system for Rwanda with smart metering, automated billing, leak detection, and mobile payment integration.

## 🌟 Features

### **Customer Features**

- ✅ **User Registration & Authentication** - Secure customer account management
- ✅ **Real-time Water Usage Monitoring** - Track consumption with smart meters
- ✅ **Automated Billing** - Monthly bills generated automatically
- ✅ **Mobile Payment Integration** - Pay bills via MTN Mobile Money, Airtel Money
- ✅ **Usage History & Analytics** - Detailed consumption reports
- ✅ **Leak Detection Alerts** - Automatic notifications for unusual usage
- ✅ **Multi-language Support** - English, Kinyarwanda, French

### **Admin Features**

- ✅ **System Dashboard** - Complete overview of water distribution network
- ✅ **User Management** - Customer registration and account management
- ✅ **Meter Installation & Management** - Smart meter deployment tracking
- ✅ **Billing Management** - Generate, track, and manage water bills
- ✅ **Payment Processing** - Handle mobile money transactions
- ✅ **Leak Detection System** - Monitor and alert for water leaks
- ✅ **Analytics & Reporting** - Revenue, consumption, and system performance reports
- ✅ **Alert Management** - Handle system alerts and customer issues

### **Technical Features**

- ✅ **IoT Integration Ready** - Smart meter data collection
- ✅ **Progressive Web App (PWA)** - Installable mobile app experience
- ✅ **Responsive Design** - Works on desktop, tablet, and mobile
- ✅ **Real-time Monitoring** - Live water usage tracking
- ✅ **Secure Authentication** - Password hashing and session management
- ✅ **SQLite Database** - Reliable data storage
- ✅ **RESTful API** - Clean API architecture for integrations

## 🚀 Quick Start

### **Prerequisites**

- Python 3.8 or higher
- pip (Python package installer)

### **Installation**

1. **Clone or download the project**

```bash
cd SmartWaterSystem
```

2. **Install dependencies**

```bash
pip install -r requirements.txt
```

3. **Run the application**

```bash
python apps.py
```

4. **Access the system**

- **Local**: http://localhost:5001
- **Network**: http://[YOUR-IP]:5001 (for mobile access)

### **Default Admin Credentials**

- **Username**: admin
- **Password**: admin123

## 📱 Mobile Access

### **For iPhone/Android:**

1. Connect your phone to the same WiFi as your computer
2. Find your computer's IP address
3. Open browser and go to: `http://[YOUR-IP]:5001`
4. Tap "Add to Home Screen" for PWA installation

## 🏗️ System Architecture

### **Backend (Python/Flask)**

```
apps.py                # Main Flask application
├── DatabaseManager    # SQLite database management
├── SmartWaterSystem   # Core business logic
├── API Routes         # RESTful endpoints
└── Authentication     # User security
```

### **Frontend (HTML/CSS/JavaScript)**

```
templates/index.html   # Main application interface
static/css/style.css   # Responsive styling
static/js/script.js    # Interactive functionality
```

### **Database Schema**

- **users** - Customer information and authentication
- **water_meters** - Smart meter installations
- **water_readings** - Consumption data from meters
- **bills** - Monthly billing information
- **payments** - Payment transaction records
- **leak_alerts** - Leak detection notifications
- **admin_users** - Administrative access control

## 🔧 API Endpoints

### **Authentication**

- `POST /api/register` - Customer registration
- `POST /api/login` - Customer login

### **Water Management**

- `POST /api/meter/install` - Install water meter
- `POST /api/reading/record` - Record meter reading
- `GET /api/user/dashboard/{user_id}` - Get customer dashboard data

### **Billing & Payments**

- `POST /api/bill/generate` - Generate monthly bill
- `POST /api/payment/process` - Process bill payment

### **Monitoring & Alerts**

- `GET /api/leak/detect/{meter_id}` - Check for leaks
- `GET /api/admin/stats` - System statistics
- `GET /api/admin/alerts` - Active leak alerts

## 💰 Revenue Model

### **Revenue Streams**

1. **Water Consumption Charges** - 500 RWF per cubic meter
2. **Monthly Service Fee** - 1,000 RWF per customer
3. **Meter Installation Fee** - One-time setup cost
4. **Premium Analytics** - Advanced usage insights
5. **API Licensing** - Third-party integrations

### **Target Market**

- **Primary**: Urban households in Kigali and secondary cities
- **Secondary**: Small businesses and institutions
- **Tertiary**: Rural communities with water cooperatives

## 🎯 Business Benefits

### **For Water Utilities**

- ✅ **Automated Billing** - Reduce manual billing errors
- ✅ **Real-time Monitoring** - Instant leak detection
- ✅ **Revenue Optimization** - Accurate consumption tracking
- ✅ **Customer Self-service** - Reduce support costs
- ✅ **Data Analytics** - Optimize water distribution

### **For Customers**

- ✅ **Transparent Billing** - Clear usage breakdown
- ✅ **Convenient Payments** - Mobile money integration
- ✅ **Usage Control** - Monitor and reduce consumption
- ✅ **Leak Alerts** - Prevent water waste and high bills
- ✅ **24/7 Access** - Check account anytime

## 🔒 Security Features

- **Password Hashing** - SHA-256 encryption
- **Input Validation** - Prevent SQL injection
- **Session Management** - Secure user sessions
- **CORS Protection** - Cross-origin request security
- **Data Encryption** - Sensitive information protection

## 📊 Analytics & Reporting

### **Customer Analytics**

- Daily/monthly consumption trends
- Cost analysis and budgeting
- Leak detection history
- Payment history

### **Admin Analytics**

- System-wide consumption patterns
- Revenue tracking and forecasting
- Leak detection effectiveness
- Customer behavior insights

## 🌍 Scalability

### **Technical Scalability**

- **Database**: Easily migrate to PostgreSQL for larger deployments
- **API**: RESTful design supports mobile apps and integrations
- **Cloud Ready**: Deploy on AWS, Google Cloud, or Azure
- **Multi-tenant**: Support multiple water utilities

### **Business Scalability**

- **Geographic**: Expand to other Rwandan cities
- **Regional**: Scale across East Africa
- **Vertical**: Add electricity, gas, and other utilities
- **Horizontal**: White-label for other countries

## 🚀 Future Enhancements

### **Phase 2 Features**

- [ ] **IoT Sensor Integration** - Real hardware meter connectivity
- [ ] **Mobile Apps** - Native iOS and Android applications
- [ ] **AI Predictions** - Machine learning for consumption forecasting
- [ ] **Blockchain Payments** - Cryptocurrency payment options

### **Phase 3 Features**

- [ ] **Smart City Integration** - Connect with city management systems
- [ ] **Environmental Monitoring** - Water quality sensors
- [ ] **Customer Portal** - Advanced self-service features
- [ ] **Multi-utility Support** - Electricity, gas, waste management

## 📞 Support & Contact

For technical support or business inquiries:

- **Email**: <EMAIL>
- **Phone**: +250 XXX XXX XXX
- **Website**: www.smartwatersystem.rw

## 📄 License

This project is proprietary software. All rights reserved.

---

**Built with ❤️ for Rwanda's Smart City Vision 2050**
