@echo off
echo Installing Mobile Loan App Dependencies...
echo.

echo Installing Flask...
pip install Flask==2.3.3

echo Installing Flask-CORS...
pip install Flask-CORS==4.0.0

echo Installing requests...
pip install requests==2.31.0

echo Installing other dependencies...
pip install Werkzeug==2.3.7
pip install Jinja2==3.1.2
pip install MarkupSafe==2.1.3
pip install itsdangerous==2.1.2
pip install click==8.1.7
pip install blinker==1.6.3

echo.
echo All dependencies installed successfully!
echo.
echo You can now run the app with: python app.py
pause
