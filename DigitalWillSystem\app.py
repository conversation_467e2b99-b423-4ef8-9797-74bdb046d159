from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
import sqlite3
import hashlib
import secrets
import uuid
from werkzeug.utils import secure_filename
import re

app = Flask(__name__)
CORS(app)

# Configuration
app.config['SECRET_KEY'] = 'digital_will_system_2024'
UPLOAD_FOLDER = 'documents'
ALLOWED_EXTENSIONS = {'pdf', 'png', 'jpg', 'jpeg', 'doc', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class DatabaseManager:
    def __init__(self):
        self.db_name = 'digital_will.db'
        self.init_database()
    
    def init_database(self):
        """Initialize database with all required tables"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                full_name TEXT NOT NULL,
                phone_number TEXT UNIQUE NOT NULL,
                email TEXT,
                national_id TEXT UNIQUE NOT NULL,
                date_of_birth DATE NOT NULL,
                address TEXT NOT NULL,
                password_hash TEXT NOT NULL,
                is_verified BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        # Wills table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS wills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                will_id TEXT UNIQUE NOT NULL,
                user_id INTEGER,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                assets_description TEXT,
                beneficiaries TEXT NOT NULL,
                executor_name TEXT NOT NULL,
                executor_contact TEXT NOT NULL,
                status TEXT DEFAULT 'draft',
                is_sealed BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                sealed_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Digital witnesses table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS digital_witnesses (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                will_id INTEGER,
                witness_name TEXT NOT NULL,
                witness_email TEXT NOT NULL,
                witness_phone TEXT NOT NULL,
                witness_id_number TEXT NOT NULL,
                signature_hash TEXT,
                witness_status TEXT DEFAULT 'pending',
                witnessed_at TIMESTAMP,
                verification_code TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (will_id) REFERENCES wills (id)
            )
        ''')
        
        # Legal validators table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS legal_validators (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                validator_name TEXT NOT NULL,
                license_number TEXT UNIQUE NOT NULL,
                email TEXT NOT NULL,
                phone_number TEXT NOT NULL,
                specialization TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Will validations table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS will_validations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                will_id INTEGER,
                validator_id INTEGER,
                validation_status TEXT DEFAULT 'pending',
                validation_notes TEXT,
                validation_fee REAL,
                validated_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (will_id) REFERENCES wills (id),
                FOREIGN KEY (validator_id) REFERENCES legal_validators (id)
            )
        ''')
        
        # Will documents table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS will_documents (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                will_id INTEGER,
                document_name TEXT NOT NULL,
                document_path TEXT NOT NULL,
                document_type TEXT NOT NULL,
                file_size INTEGER,
                uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (will_id) REFERENCES wills (id)
            )
        ''')
        
        # Inheritance claims table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS inheritance_claims (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                will_id INTEGER,
                claimant_name TEXT NOT NULL,
                claimant_contact TEXT NOT NULL,
                relationship TEXT NOT NULL,
                claim_description TEXT,
                supporting_documents TEXT,
                claim_status TEXT DEFAULT 'submitted',
                submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reviewed_at TIMESTAMP,
                FOREIGN KEY (will_id) REFERENCES wills (id)
            )
        ''')
        
        # Payments table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                will_id INTEGER,
                payment_type TEXT NOT NULL,
                amount REAL NOT NULL,
                payment_method TEXT NOT NULL,
                transaction_id TEXT,
                payment_status TEXT DEFAULT 'pending',
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (will_id) REFERENCES wills (id)
            )
        ''')
        
        # Admin users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT DEFAULT 'admin',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # Create default admin user and legal validators
        self.create_default_data()
    
    def create_default_data(self):
        """Create default admin user and sample legal validators"""
        conn = sqlite3.connect(self.db_name)
        cursor = conn.cursor()
        
        # Create default admin
        cursor.execute('SELECT COUNT(*) FROM admin_users WHERE username = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            password_hash = hashlib.sha256('admin123'.encode()).hexdigest()
            cursor.execute('''
                INSERT INTO admin_users (username, password_hash, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ('admin', password_hash, 'System Administrator', 'super_admin'))
        
        # Create sample legal validators
        cursor.execute('SELECT COUNT(*) FROM legal_validators')
        if cursor.fetchone()[0] == 0:
            validators = [
                ('Advocate John Mugisha', 'RBA001', '<EMAIL>', '+250788123456', 'Inheritance Law'),
                ('Advocate Sarah Uwimana', 'RBA002', '<EMAIL>', '+250788654321', 'Family Law'),
                ('Advocate David Nkurunziza', 'RBA003', '<EMAIL>', '+250788789012', 'Estate Planning')
            ]
            
            for validator in validators:
                cursor.execute('''
                    INSERT INTO legal_validators (validator_name, license_number, email, phone_number, specialization)
                    VALUES (?, ?, ?, ?, ?)
                ''', validator)
        
        conn.commit()
        conn.close()
    
    def get_connection(self):
        """Get database connection"""
        return sqlite3.connect(self.db_name)

class DigitalWillSystem:
    def __init__(self):
        self.db = DatabaseManager()
        self.will_creation_fee = 25000  # RWF for creating a will
        self.validation_fee = 50000     # RWF for legal validation
        self.storage_fee_annual = 10000 # RWF annual storage fee
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def generate_will_id(self):
        """Generate unique will ID"""
        return f"DW{datetime.datetime.now().strftime('%Y%m%d')}{secrets.token_hex(4).upper()}"
    
    def register_user(self, full_name, phone_number, email, national_id, date_of_birth, address, password):
        """Register new user"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            # Check if user already exists
            cursor.execute('SELECT id FROM users WHERE phone_number = ? OR national_id = ?', 
                         (phone_number, national_id))
            if cursor.fetchone():
                return False, "User with this phone number or National ID already exists"
            
            password_hash = self.hash_password(password)
            
            cursor.execute('''
                INSERT INTO users (full_name, phone_number, email, national_id, date_of_birth, address, password_hash)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (full_name, phone_number, email, national_id, date_of_birth, address, password_hash))
            
            user_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return True, f"User registered successfully with ID: {user_id}"
            
        except Exception as e:
            return False, f"Registration failed: {str(e)}"
    
    def authenticate_user(self, phone_number, password):
        """Authenticate user login"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()
            
            password_hash = self.hash_password(password)
            cursor.execute('''
                SELECT id, full_name, phone_number, email, national_id, is_verified 
                FROM users 
                WHERE phone_number = ? AND password_hash = ? AND is_active = 1
            ''', (phone_number, password_hash))
            
            user = cursor.fetchone()
            conn.close()
            
            if user:
                return True, {
                    'id': user[0],
                    'full_name': user[1],
                    'phone_number': user[2],
                    'email': user[3],
                    'national_id': user[4],
                    'is_verified': user[5]
                }
            else:
                return False, "Invalid credentials"
                
        except Exception as e:
            return False, f"Authentication failed: {str(e)}"

    def create_will(self, user_id, title, content, assets_description, beneficiaries, executor_name, executor_contact):
        """Create a new digital will"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            will_id = self.generate_will_id()

            cursor.execute('''
                INSERT INTO wills (will_id, user_id, title, content, assets_description,
                                 beneficiaries, executor_name, executor_contact, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'draft')
            ''', (will_id, user_id, title, content, assets_description,
                  beneficiaries, executor_name, executor_contact))

            will_db_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return True, {
                'will_id': will_id,
                'db_id': will_db_id,
                'message': 'Will created successfully'
            }

        except Exception as e:
            return False, f"Will creation failed: {str(e)}"

    def add_digital_witness(self, will_id, witness_name, witness_email, witness_phone, witness_id_number):
        """Add digital witness to a will"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get will database ID
            cursor.execute('SELECT id FROM wills WHERE will_id = ?', (will_id,))
            will_record = cursor.fetchone()
            if not will_record:
                return False, "Will not found"

            will_db_id = will_record[0]
            verification_code = secrets.token_hex(8).upper()

            cursor.execute('''
                INSERT INTO digital_witnesses (will_id, witness_name, witness_email, witness_phone,
                                             witness_id_number, verification_code, witness_status)
                VALUES (?, ?, ?, ?, ?, ?, 'pending')
            ''', (will_db_id, witness_name, witness_email, witness_phone, witness_id_number, verification_code))

            conn.commit()
            conn.close()

            # In real system, send email/SMS with verification code
            return True, {
                'verification_code': verification_code,
                'message': 'Digital witness added successfully'
            }

        except Exception as e:
            return False, f"Adding witness failed: {str(e)}"

    def seal_will(self, will_id, user_id):
        """Seal the will (make it immutable)"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Verify will belongs to user and has required witnesses
            cursor.execute('''
                SELECT w.id, COUNT(dw.id) as witness_count
                FROM wills w
                LEFT JOIN digital_witnesses dw ON w.id = dw.will_id AND dw.witness_status = 'verified'
                WHERE w.will_id = ? AND w.user_id = ? AND w.is_sealed = 0
                GROUP BY w.id
            ''', (will_id, user_id))

            result = cursor.fetchone()
            if not result:
                return False, "Will not found or already sealed"

            will_db_id, witness_count = result

            if witness_count < 2:
                return False, "At least 2 verified witnesses required to seal the will"

            # Seal the will
            cursor.execute('''
                UPDATE wills
                SET is_sealed = 1, status = 'sealed', sealed_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (will_db_id,))

            conn.commit()
            conn.close()

            return True, "Will sealed successfully"

        except Exception as e:
            return False, f"Sealing will failed: {str(e)}"

    def request_legal_validation(self, will_id, validator_id):
        """Request legal validation for a will"""
        try:
            conn = self.db.get_connection()
            cursor = conn.cursor()

            # Get will database ID
            cursor.execute('SELECT id FROM wills WHERE will_id = ?', (will_id,))
            will_record = cursor.fetchone()
            if not will_record:
                return False, "Will not found"

            will_db_id = will_record[0]

            cursor.execute('''
                INSERT INTO will_validations (will_id, validator_id, validation_status, validation_fee)
                VALUES (?, ?, 'pending', ?)
            ''', (will_db_id, validator_id, self.validation_fee))

            conn.commit()
            conn.close()

            return True, f"Legal validation requested. Fee: {self.validation_fee} RWF"

        except Exception as e:
            return False, f"Validation request failed: {str(e)}"

# Initialize the system
will_system = DigitalWillSystem()

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/register', methods=['POST'])
def register_user():
    try:
        data = request.get_json()
        full_name = data.get('full_name')
        phone_number = data.get('phone_number')
        email = data.get('email', '')
        national_id = data.get('national_id')
        date_of_birth = data.get('date_of_birth')
        address = data.get('address')
        password = data.get('password')
        
        if not all([full_name, phone_number, national_id, date_of_birth, address, password]):
            return jsonify({'success': False, 'message': 'All required fields must be filled'}), 400
        
        success, message = will_system.register_user(full_name, phone_number, email, national_id, date_of_birth, address, password)
        
        return jsonify({'success': success, 'message': message})
        
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/login', methods=['POST'])
def login_user():
    try:
        data = request.get_json()
        phone_number = data.get('phone_number')
        password = data.get('password')
        
        if not all([phone_number, password]):
            return jsonify({'success': False, 'message': 'Phone number and password are required'}), 400
        
        success, result = will_system.authenticate_user(phone_number, password)
        
        if success:
            return jsonify({'success': True, 'user': result})
        else:
            return jsonify({'success': False, 'message': result}), 401
            
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/will/create', methods=['POST'])
def create_will():
    try:
        data = request.get_json()
        user_id = data.get('user_id')
        title = data.get('title')
        content = data.get('content')
        assets_description = data.get('assets_description')
        beneficiaries = data.get('beneficiaries')
        executor_name = data.get('executor_name')
        executor_contact = data.get('executor_contact')

        if not all([user_id, title, content, beneficiaries, executor_name, executor_contact]):
            return jsonify({'success': False, 'message': 'All required fields must be filled'}), 400

        success, result = will_system.create_will(user_id, title, content, assets_description,
                                                 beneficiaries, executor_name, executor_contact)

        if success:
            return jsonify({'success': True, 'will': result})
        else:
            return jsonify({'success': False, 'message': result})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/will/add-witness', methods=['POST'])
def add_witness():
    try:
        data = request.get_json()
        will_id = data.get('will_id')
        witness_name = data.get('witness_name')
        witness_email = data.get('witness_email')
        witness_phone = data.get('witness_phone')
        witness_id_number = data.get('witness_id_number')

        if not all([will_id, witness_name, witness_email, witness_phone, witness_id_number]):
            return jsonify({'success': False, 'message': 'All witness fields are required'}), 400

        success, result = will_system.add_digital_witness(will_id, witness_name, witness_email,
                                                         witness_phone, witness_id_number)

        if success:
            return jsonify({'success': True, 'witness': result})
        else:
            return jsonify({'success': False, 'message': result})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/will/seal', methods=['POST'])
def seal_will():
    try:
        data = request.get_json()
        will_id = data.get('will_id')
        user_id = data.get('user_id')

        if not all([will_id, user_id]):
            return jsonify({'success': False, 'message': 'Will ID and User ID are required'}), 400

        success, message = will_system.seal_will(will_id, user_id)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/will/request-validation', methods=['POST'])
def request_validation():
    try:
        data = request.get_json()
        will_id = data.get('will_id')
        validator_id = data.get('validator_id')

        if not all([will_id, validator_id]):
            return jsonify({'success': False, 'message': 'Will ID and Validator ID are required'}), 400

        success, message = will_system.request_legal_validation(will_id, validator_id)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/user/wills/<int:user_id>', methods=['GET'])
def get_user_wills(user_id):
    try:
        conn = will_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT will_id, title, status, is_sealed, created_at, last_updated
            FROM wills
            WHERE user_id = ?
            ORDER BY created_at DESC
        ''', (user_id,))

        wills = cursor.fetchall()
        conn.close()

        will_list = []
        for will in wills:
            will_list.append({
                'will_id': will[0],
                'title': will[1],
                'status': will[2],
                'is_sealed': will[3],
                'created_at': will[4],
                'last_updated': will[5]
            })

        return jsonify({'success': True, 'wills': will_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/legal-validators', methods=['GET'])
def get_legal_validators():
    try:
        conn = will_system.db.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT id, validator_name, license_number, specialization, email, phone_number
            FROM legal_validators
            WHERE is_active = 1
            ORDER BY validator_name
        ''')

        validators = cursor.fetchall()
        conn.close()

        validator_list = []
        for validator in validators:
            validator_list.append({
                'id': validator[0],
                'name': validator[1],
                'license': validator[2],
                'specialization': validator[3],
                'email': validator[4],
                'phone': validator[5]
            })

        return jsonify({'success': True, 'validators': validator_list})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/login', methods=['POST'])
def admin_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')

        if not all([username, password]):
            return jsonify({'success': False, 'message': 'Username and password are required'}), 400

        # Simple admin authentication
        if username == 'admin' and password == 'admin123':
            return jsonify({
                'success': True,
                'admin': {
                    'username': username,
                    'role': 'super_admin',
                    'full_name': 'System Administrator'
                }
            })
        else:
            return jsonify({'success': False, 'message': 'Invalid admin credentials'}), 401

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5002)
