# Mobile Loan Scoring App for Informal Workers

A revolutionary fintech platform that provides instant loans to informal workers in Rwanda using mobile money transaction history, airtime purchases, and savings behavior for credit scoring.

## 🌟 Features

### **Customer Features**
- ✅ **Instant Credit Scoring** - AI-powered analysis of mobile money behavior
- ✅ **No Collateral Loans** - Digital footprint serves as credit history
- ✅ **Quick Approval** - Loan decisions in under 5 minutes
- ✅ **Flexible Terms** - 7-90 day loan periods
- ✅ **Mobile Integration** - Works with MTN and Airtel mobile money
- ✅ **Real-time Dashboard** - Track loans, payments, and credit score
- ✅ **Multi-language Support** - English, French, and Kinyarwanda

### **Smart Credit Scoring Algorithm**
- ✅ **Transaction Frequency** - Regular mobile money usage (30 points)
- ✅ **Transaction Volume** - Average transaction amounts (25 points)
- ✅ **Savings Behavior** - Mobile savings patterns (25 points)
- ✅ **Airtime Consistency** - Regular airtime purchases (20 points)
- ✅ **Risk Assessment** - Automated risk categorization
- ✅ **Dynamic Scoring** - Credit score updates with new data

### **Loan Features**
- ✅ **Instant Approval** - Automated decision making
- ✅ **Flexible Amounts** - 10,000 to 500,000 RWF
- ✅ **Competitive Rates** - Starting from 15% based on credit score
- ✅ **Mobile Disbursement** - Funds sent directly to mobile wallet
- ✅ **Flexible Repayment** - Daily, weekly, or monthly options

## 🚀 Quick Start

### **Prerequisites**
- Python 3.8 or higher
- pip (Python package installer)

### **Installation**

1. **Navigate to the project folder**
```bash
cd MobileLoanApp
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Run the application**
```bash
python app.py
```

4. **Access the system**
- **Local**: http://localhost:5003
- **Network**: http://[YOUR-IP]:5003 (for mobile access)

### **Default Admin Credentials**
- **Username**: admin
- **Password**: admin123

## 📱 Mobile Access

### **For iPhone/Android:**
1. Connect your phone to the same WiFi as your computer
2. Find your computer's IP address
3. Open browser and go to: `http://[YOUR-IP]:5003`
4. Tap "Add to Home Screen" for PWA installation

## 🏗️ System Architecture

### **Backend (Python/Flask)**
```
app.py                 # Main Flask application
├── DatabaseManager    # SQLite database management
├── MobileLoanSystem   # Core business logic
├── Credit Scoring     # AI-powered scoring algorithm
├── API Routes         # RESTful endpoints
└── Authentication     # User security
```

### **Frontend (HTML/CSS/JavaScript)**
```
templates/index.html   # Main application interface
static/css/style.css   # Green-themed fintech styling
static/js/script.js    # Interactive functionality
```

### **Database Schema**
- **users** - User registration and profiles
- **mobile_transactions** - Mobile money transaction history
- **airtime_purchases** - Airtime purchase records
- **savings_history** - Mobile savings behavior
- **loan_applications** - Loan requests and approvals
- **loan_repayments** - Payment tracking
- **credit_score_history** - Credit score evolution

## 🔧 API Endpoints

### **Authentication**
- `POST /api/register` - User registration
- `POST /api/login` - User login

### **Mobile Data**
- `POST /api/mobile-data/sync` - Sync mobile money data
- `GET /api/user/dashboard/{user_id}` - User dashboard data

### **Loan Management**
- `POST /api/loan/apply` - Apply for loan
- `GET /api/loan/status/{loan_id}` - Check loan status
- `POST /api/loan/repay` - Make loan repayment

### **Credit Scoring**
- `GET /api/credit-score/{user_id}` - Get current credit score
- `POST /api/credit-score/recalculate` - Recalculate score

## 💰 Credit Scoring Algorithm

### **Scoring Factors**
1. **Mobile Money Frequency (30 points)**
   - Daily transactions: 30 points
   - Weekly transactions: 20 points
   - Monthly transactions: 10 points

2. **Transaction Volume (25 points)**
   - >50,000 RWF average: 25 points
   - >20,000 RWF average: 15 points
   - >5,000 RWF average: 10 points

3. **Savings Behavior (25 points)**
   - >100,000 RWF saved: 25 points
   - >50,000 RWF saved: 15 points
   - >10,000 RWF saved: 10 points

4. **Airtime Consistency (20 points)**
   - >10 purchases/month: 20 points
   - >5 purchases/month: 15 points
   - >2 purchases/month: 10 points

### **Credit Score Ranges**
- **700-850**: Excellent (Full loan amount, 15% interest)
- **600-699**: Good (80% loan amount, 20% interest)
- **500-599**: Fair (60% loan amount, 25% interest)
- **300-499**: Poor (Loan rejected)

## 🎯 Target Market

### **Primary Users**
- **Informal Workers**: Traders, farmers, artisans, drivers
- **Small Business Owners**: Market vendors, service providers
- **Gig Workers**: Motorcycle taxi drivers, delivery workers
- **Rural Population**: Farmers, cooperative members

### **Market Size (Rwanda)**
- **Informal Workers**: 2.5+ million people
- **Mobile Money Users**: 8+ million active users
- **Unbanked Population**: 60% of adults
- **Market Value**: $200M+ annually

## 💡 Business Model

### **Revenue Streams**
1. **Interest Income** - 15-25% annual interest rates
2. **Processing Fees** - 2% loan origination fee
3. **Late Payment Fees** - 5% penalty on overdue amounts
4. **Premium Features** - Advanced analytics and insights
5. **Partner Commissions** - Revenue sharing with mobile operators

### **Unit Economics**
- **Average Loan**: 100,000 RWF
- **Average Interest**: 20% (20,000 RWF)
- **Processing Fee**: 2,000 RWF
- **Total Revenue per Loan**: 22,000 RWF
- **Default Rate**: 5% (manageable with AI scoring)

## 🔒 Security Features

### **Data Protection**
- **Encrypted Storage** - All sensitive data encrypted
- **Secure API** - HTTPS and token-based authentication
- **Privacy Compliance** - GDPR-compliant data handling
- **Fraud Detection** - AI-powered fraud prevention

### **Financial Security**
- **Risk Assessment** - Multi-factor risk evaluation
- **Automated Limits** - Dynamic loan limits based on behavior
- **Real-time Monitoring** - Continuous transaction monitoring
- **Regulatory Compliance** - Adheres to Rwanda financial regulations

## 📊 Competitive Advantages

### **Technology**
1. **AI-Powered Scoring** - Advanced machine learning algorithms
2. **Real-time Processing** - Instant loan decisions
3. **Mobile-First Design** - Optimized for smartphones
4. **Offline Capability** - PWA works without internet

### **Market Position**
1. **First Mover** - First AI-powered loan app for informal workers
2. **Local Focus** - Built specifically for Rwanda market
3. **Language Support** - Native Kinyarwanda interface
4. **Cultural Understanding** - Designed for informal economy

## 🌍 Scalability

### **Technical Scalability**
- **Cloud Ready** - Deploy on AWS, Google Cloud, or Azure
- **Microservices** - Modular architecture for easy scaling
- **API-First** - RESTful design supports mobile apps
- **Database Scaling** - Easy migration to PostgreSQL/MongoDB

### **Business Scalability**
- **Regional Expansion** - Scale across East Africa
- **Product Extensions** - Savings, insurance, investments
- **Partnership Integration** - Banks, MFIs, mobile operators
- **White-label Solutions** - License technology to other markets

## 🚀 Future Enhancements

### **Phase 2 Features**
- [ ] **Machine Learning Models** - Advanced credit scoring algorithms
- [ ] **Blockchain Integration** - Immutable credit history
- [ ] **Insurance Products** - Loan protection insurance
- [ ] **Savings Products** - High-yield mobile savings accounts

### **Phase 3 Features**
- [ ] **Investment Platform** - Micro-investment opportunities
- [ ] **Business Loans** - Larger loans for small businesses
- [ ] **International Remittances** - Cross-border money transfers
- [ ] **Cryptocurrency Support** - Digital currency integration

## 📞 Support & Contact

For technical support or business inquiries:
- **Email**: <EMAIL>
- **Phone**: +250 XXX XXX XXX
- **Website**: www.mobileloan.rw

## 📄 Legal Disclaimer

This system is designed to comply with Rwanda's financial regulations. Users are advised to borrow responsibly and within their means.

## 📄 License

This project is proprietary software. All rights reserved.

---

**Empowering Rwanda's Informal Workers Through Smart Lending** 📱💰✨
