from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import json
import os
import datetime
from difflib import SequenceMatcher
import re
from werkzeug.utils import secure_filename

app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx'}
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

class DuplicateDetectionAI:
    """AI system to detect duplicate e-pass requests within a 24-hour period."""

    def __init__(self):
        self.similarity_threshold = 0.8  # Similarity threshold for text matching
        self.time_window_hours = 24  # Time window in hours

    def calculate_text_similarity(self, text1, text2):
        """Calculate similarity ratio between two text strings."""
        return SequenceMatcher(None, str(text1).lower(), str(text2).lower()).ratio()

    def normalize_contact(self, contact):
        """Normalize contact information by removing non-digit characters."""
        return re.sub(r'\D', '', str(contact))

    def is_potential_duplicate(self, new_request, existing_requests):
        """
        Check if a new request is potentially a duplicate.
        Returns (is_duplicate, duplicate_request_id, confidence_score, detection_reason)
        """
        # Extract current timestamp
        current_time = datetime.datetime.now()

        # Get relevant fields from the new request
        new_name = str(new_request["name"]).lower()
        new_contact = self.normalize_contact(new_request["contact"])
        new_id_proof = str(new_request["id_proof"]).lower()
        new_reason = str(new_request["reason"]).lower()

        highest_confidence = 0
        duplicate_id = None
        detection_reason = ""

        for request in existing_requests:
            # Skip comparing with the same request
            if "request_id" in new_request and request["request_id"] == new_request["request_id"]:
                continue

            # Only check requests within the time window
            if "timestamp" not in request:
                continue

            request_time = datetime.datetime.fromisoformat(request["timestamp"])
            time_diff = (current_time - request_time).total_seconds() / 3600

            if time_diff > self.time_window_hours:
                continue

            # Calculate similarities
            name_similarity = self.calculate_text_similarity(new_name, request["name"].lower())
            contact_similarity = self.calculate_text_similarity(new_contact, self.normalize_contact(request["contact"]))
            id_similarity = self.calculate_text_similarity(new_id_proof, request["id_proof"].lower())
            reason_similarity = self.calculate_text_similarity(new_reason, request["reason"].lower())

            # Weighted confidence score
            confidence = (name_similarity * 0.4 +
                          contact_similarity * 0.3 +
                          id_similarity * 0.2 +
                          reason_similarity * 0.1)

            # Exact contact match is a strong indicator
            if new_contact and new_contact == self.normalize_contact(request["contact"]):
                confidence = max(confidence, 0.7)  # Set minimum confidence to 0.7 for contact match

            # Exact ID proof match is a very strong indicator
            if new_id_proof and new_id_proof == request["id_proof"].lower():
                confidence = max(confidence, 0.9)  # Set minimum confidence to 0.9 for ID match

            # Update if this is the highest confidence duplicate so far
            if confidence > highest_confidence and confidence >= self.similarity_threshold:
                highest_confidence = confidence
                duplicate_id = request["request_id"]

                # Determine the primary reason for the duplicate detection
                if contact_similarity > 0.9:
                    detection_reason = "Same contact information"
                elif name_similarity > 0.9:
                    detection_reason = "Same name"
                elif id_similarity > 0.9:
                    detection_reason = "Same ID proof"
                elif reason_similarity > 0.9:
                    detection_reason = "Very similar reason"
                else:
                    detection_reason = "Multiple matching factors"

        is_duplicate = highest_confidence >= self.similarity_threshold
        return (is_duplicate, duplicate_id, highest_confidence, detection_reason)


class CurfewEPassSystem:
    def __init__(self):
        self.requests = []  # Store all e-pass requests
        self.admin_credentials = {"Prince": "Admin123"}  # Admin login details
        self.current_request_id = 1  # Auto-increment request ID
        self.data_file = "requests.json"  # File to save requests
        self.duplicate_detector = DuplicateDetectionAI()  # Initialize the AI duplicate detector
        self.load_requests()  # Load requests from file on startup

    def load_requests(self):
        """Load requests from the JSON file."""
        if os.path.exists(self.data_file):
            with open(self.data_file, "r") as file:
                data = json.load(file)
                self.requests = data.get("requests", [])
                self.current_request_id = data.get("current_request_id", 1)

    def save_requests(self):
        """Save requests to the JSON file."""
        data = {
            "requests": self.requests,
            "current_request_id": self.current_request_id,
        }
        with open(self.data_file, "w") as file:
            json.dump(data, file)

    def register_request(self, name, contact, id_proof, reason, attachment):
        # Create the new request
        request = {
            "request_id": self.current_request_id,
            "name": name,
            "contact": contact,
            "id_proof": id_proof,
            "reason": reason,
            "attachment": attachment,
            "status": "Pending",
            "e_pass_id": None,
            "appeal": None,
            "timestamp": datetime.datetime.now().isoformat()  # Add timestamp    
        }

        # Check for potential duplicates
        is_duplicate, duplicate_id, confidence, reason = self.duplicate_detector.is_potential_duplicate(
            request, self.requests
        )

        if is_duplicate:
            return (False, f"Potential duplicate of Request #{duplicate_id} detected ({confidence:.0%} confidence). " +
                    f"Reason: {reason}. Please wait 24 hours between similar requests or contact support.")

        # Not a duplicate, proceed with registration
        self.requests.append(request)
        self.current_request_id += 1
        self.save_requests()  # Save requests after adding a new one
        return (True, f"Request submitted successfully! Your request ID is {request['request_id']}.")

    def get_request_status(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                return request
        return None

    def approve_request(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                if request["status"] == "Pending":
                    request["status"] = "Approved"
                    request["e_pass_id"] = f"EP-{request_id:05}"
                    self.save_requests()  # Save requests after approval
                    return f"Request {request_id} approved. E-Pass ID: {request['e_pass_id']}"
                return "Request already processed."
        return "Request ID not found."

    def deny_request(self, request_id):
        for request in self.requests:
            if request["request_id"] == request_id:
                if request["status"] == "Pending":
                    request["status"] = "Denied"
                    self.save_requests()  # Save requests after denial
                    return f"Request {request_id} denied."
                return "Request already processed."
        return "Request ID not found."

    def delete_request(self, request_id):
        """Delete a request by request ID (only accessible by admin)."""
        for request in self.requests:
            if request["request_id"] == request_id:
                self.requests.remove(request)
                self.save_requests()  # Save after deletion
                return f"Request {request_id} deleted."
        return "Request ID not found."

    def delete_all_requests(self):
        """Delete all requests and reset the request ID to 1."""
        self.requests.clear()  # Clear all requests
        self.current_request_id = 1  # Reset the request ID counter
        self.save_requests()  # Save after clearing all requests
        return "All requests have been deleted, and the ID counter has been reset to 1."

    def get_admin_stats(self):
        """Generate statistics for admin panel (without duplicate information)."""
        total_requests = len(self.requests)
        approved_count = sum(1 for request in self.requests if request["status"] == "Approved")
        denied_count = sum(1 for request in self.requests if request["status"] == "Denied")
        pending_count = sum(1 for request in self.requests if request["status"] == "Pending")

        return {
            "total_requests": total_requests,
            "approved_requests": approved_count,
            "denied_requests": denied_count,
            "pending_requests": pending_count
        }

    def authenticate_admin(self, username, password):
        """Authenticate admin credentials."""
        return username in self.admin_credentials and self.admin_credentials[username] == password

# Initialize the system
epass_system = CurfewEPassSystem()

# Routes
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/submit-request', methods=['POST'])
def submit_request():
    try:
        # Handle both JSON and form data
        if request.is_json:
            data = request.get_json()
            name = data.get('name')
            contact = data.get('contact')
            id_proof = data.get('id_proof')
            reason = data.get('reason')
            attachment = data.get('attachment', 'No file selected.')
        else:
            # Handle form data with file upload
            name = request.form.get('name')
            contact = request.form.get('contact')
            id_proof = request.form.get('id_proof')
            reason = request.form.get('reason')

            # Handle file upload
            attachment = 'No file selected.'
            if 'attachment' in request.files:
                file = request.files['attachment']
                if file and file.filename != '' and allowed_file(file.filename):
                    filename = secure_filename(file.filename)
                    # Add timestamp to avoid filename conflicts
                    import time
                    timestamp = str(int(time.time()))
                    filename = f"{timestamp}_{filename}"
                    file.save(os.path.join(app.config['UPLOAD_FOLDER'], filename))
                    attachment = filename

        if not all([name, contact, id_proof, reason]):
            return jsonify({'success': False, 'message': 'All fields except attachment are required!'}), 400

        success, message = epass_system.register_request(name, contact, id_proof, reason, attachment)

        return jsonify({'success': success, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/check-status/<int:request_id>', methods=['GET'])
def check_status(request_id):
    try:
        request_data = epass_system.get_request_status(request_id)
        
        if request_data:
            return jsonify({'success': True, 'request': request_data})
        else:
            return jsonify({'success': False, 'message': 'Request ID not found.'}), 404
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin-login', methods=['POST'])
def admin_login():
    try:
        data = request.get_json()
        username = data.get('username')
        password = data.get('password')
        
        if epass_system.authenticate_admin(username, password):
            return jsonify({'success': True, 'message': 'Login successful'})
        else:
            return jsonify({'success': False, 'message': 'Invalid credentials!'}), 401
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/requests', methods=['GET'])
def get_all_requests():
    try:
        stats = epass_system.get_admin_stats()
        return jsonify({
            'success': True,
            'requests': epass_system.requests,
            'stats': stats
        })

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/approve/<int:request_id>', methods=['POST'])
def approve_request(request_id):
    try:
        message = epass_system.approve_request(request_id)
        return jsonify({'success': True, 'message': message})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/deny/<int:request_id>', methods=['POST'])
def deny_request(request_id):
    try:
        message = epass_system.deny_request(request_id)
        return jsonify({'success': True, 'message': message})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/delete/<int:request_id>', methods=['DELETE'])
def delete_request(request_id):
    try:
        message = epass_system.delete_request(request_id)
        return jsonify({'success': True, 'message': message})
    
    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/api/admin/delete-all', methods=['DELETE'])
def delete_all_requests():
    try:
        message = epass_system.delete_all_requests()
        return jsonify({'success': True, 'message': message})

    except Exception as e:
        return jsonify({'success': False, 'message': str(e)}), 500

@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Serve uploaded files"""
    try:
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    except Exception as e:
        return jsonify({'success': False, 'message': 'File not found'}), 404

# PWA routes are registered in app_pwa.py when running PWA version

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
