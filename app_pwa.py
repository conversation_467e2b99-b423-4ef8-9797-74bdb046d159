# PWA-enabled version of the E-Pass System
# This file imports your original app.py and adds PWA functionality

from app import app
from pwa_routes import register_pwa_routes

# Register PWA routes with the existing app
app = register_pwa_routes(app)

if __name__ == '__main__':
    print("🚀 Starting E-Pass System with PWA features...")
    print("📱 PWA features enabled:")
    print("   ✅ Offline functionality")
    print("   ✅ Install prompts")
    print("   ✅ Mobile responsive")
    print("   ✅ Push notifications ready")
    print("   ✅ App shortcuts")
    print("")
    print("🌐 Access your app at:")
    print("   Local: http://localhost:5000")
    print("   Network: http://0.0.0.0:5000")
    print("")
    print("📱 To test PWA features:")
    print("   1. Open in Chrome/Edge on mobile or desktop")
    print("   2. Look for 'Install App' button or browser prompt")
    print("   3. Try going offline to test offline functionality")
    print("")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
