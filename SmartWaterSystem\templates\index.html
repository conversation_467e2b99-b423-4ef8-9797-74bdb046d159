<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Smart Water Distribution & Billing System</title>

    <!-- PWA Meta Tags -->
    <meta
      name="description"
      content="Smart water distribution and billing system with IoT monitoring and mobile payments"
    />
    <meta name="theme-color" content="#2563eb" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Smart Water System" />

    <!-- CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}" />
    
    <!-- Font Awesome -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
  </head>

  <body>
    <div class="container">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <i class="fas fa-tint"></i>
          <span>Smart Water System</span>
        </div>
        <div class="nav-links">
          <a href="#" onclick="showHome()" class="nav-link active">Home</a>
          <a href="#" onclick="showLogin()" class="nav-link">Login</a>
          <a href="#" onclick="showRegister()" class="nav-link">Register</a>
          <a href="#" onclick="showAdminLogin()" class="nav-link">Admin</a>
        </div>
      </nav>

      <!-- Home Screen -->
      <div id="home-screen" class="screen active">
        <div class="hero-section">
          <div class="hero-content">
            <h1><i class="fas fa-tint"></i> Smart Water Distribution System</h1>
            <p class="hero-subtitle">
              Intelligent water management with IoT monitoring and mobile billing
            </p>
            <div class="hero-stats">
              <div class="stat-card">
                <i class="fas fa-clock"></i>
                <h3>24/7</h3>
                <p>Monitoring</p>
              </div>
              <div class="stat-card">
                <i class="fas fa-mobile-alt"></i>
                <h3>Mobile</h3>
                <p>Payments</p>
              </div>
              <div class="stat-card">
                <i class="fas fa-chart-line"></i>
                <h3>Smart</h3>
                <p>Analytics</p>
              </div>
            </div>
          </div>
        </div>

        <div class="action-cards">
          <div class="card" onclick="showLogin()">
            <div class="card-icon">
              <i class="fas fa-user"></i>
            </div>
            <h3>Customer Portal</h3>
            <p>View your water usage, bills, and make payments</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <div class="card" onclick="showRegister()">
            <div class="card-icon">
              <i class="fas fa-user-plus"></i>
            </div>
            <h3>New Customer</h3>
            <p>Register for water services and meter installation</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>

          <div class="card" onclick="showAdminLogin()">
            <div class="card-icon">
              <i class="fas fa-cogs" style="color: #2563eb"></i>
            </div>
            <h3>Admin Panel</h3>
            <p>Manage water distribution, billing, and system monitoring</p>
            <div class="card-arrow">
              <i class="fas fa-arrow-right"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Login Screen -->
      <div id="login-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-sign-in-alt"></i> Customer Login</h2>
            <p>Access your water account and billing information</p>
          </div>

          <form id="login-form" class="form">
            <div class="form-group">
              <label for="login_phone">Phone Number</label>
              <input
                type="tel"
                id="login_phone"
                name="phone_number"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div class="form-group">
              <label for="login_password">Password</label>
              <input
                type="password"
                id="login_password"
                name="password"
                placeholder="Enter your password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Register Screen -->
      <div id="register-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-user-plus"></i> New Customer Registration</h2>
            <p>Register for water services and meter installation</p>
          </div>

          <form id="register-form" class="form">
            <div class="form-group">
              <label for="full_name">Full Name</label>
              <input
                type="text"
                id="full_name"
                name="full_name"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div class="form-group">
              <label for="phone_number">Phone Number</label>
              <input
                type="tel"
                id="phone_number"
                name="phone_number"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div class="form-group">
              <label for="email">Email (Optional)</label>
              <input
                type="email"
                id="email"
                name="email"
                placeholder="Enter your email address"
              />
            </div>

            <div class="form-group">
              <label for="address">Address</label>
              <textarea
                id="address"
                name="address"
                placeholder="Enter your complete address"
                required
              ></textarea>
            </div>

            <div class="form-group">
              <label for="id_number">National ID Number</label>
              <input
                type="text"
                id="id_number"
                name="id_number"
                placeholder="Enter your national ID number"
                required
              />
            </div>

            <div class="form-group">
              <label for="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                placeholder="Create a secure password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Register
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Customer Dashboard Screen -->
      <div id="dashboard-screen" class="screen">
        <div class="dashboard-container">
          <div class="dashboard-header">
            <div class="user-info">
              <h2><i class="fas fa-tachometer-alt"></i> Water Dashboard</h2>
              <p id="user-welcome">Welcome back!</p>
            </div>
            <button class="btn btn-secondary" onclick="logout()">
              <i class="fas fa-sign-out-alt"></i> Logout
            </button>
          </div>

          <div class="dashboard-stats">
            <div class="stat-card">
              <i class="fas fa-tint"></i>
              <h3 id="current-usage">0</h3>
              <p>Current Usage (m³)</p>
            </div>
            <div class="stat-card">
              <i class="fas fa-money-bill-wave"></i>
              <h3 id="current-bill">0</h3>
              <p>Current Bill (RWF)</p>
            </div>
            <div class="stat-card">
              <i class="fas fa-calendar-alt"></i>
              <h3 id="due-date">-</h3>
              <p>Due Date</p>
            </div>
          </div>

          <div class="dashboard-actions">
            <button class="btn btn-primary" onclick="showPaymentForm()">
              <i class="fas fa-credit-card"></i> Pay Bill
            </button>
            <button class="btn btn-info" onclick="viewUsageHistory()">
              <i class="fas fa-chart-bar"></i> Usage History
            </button>
            <button class="btn btn-warning" onclick="reportIssue()">
              <i class="fas fa-exclamation-triangle"></i> Report Issue
            </button>
          </div>
        </div>
      </div>

      <!-- Admin Login Screen -->
      <div id="admin-login-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-shield-alt"></i> Admin Login</h2>
            <p>Administrative access to water system management</p>
          </div>

          <form id="admin-login-form" class="form">
            <div class="form-group">
              <label for="admin_username">Username</label>
              <input
                type="text"
                id="admin_username"
                name="username"
                placeholder="Enter admin username"
                required
              />
            </div>

            <div class="form-group">
              <label for="admin_password">Password</label>
              <input
                type="password"
                id="admin_password"
                name="password"
                placeholder="Enter admin password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-shield-alt"></i> Login
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Loading Overlay -->
      <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Processing...</p>
        </div>
      </div>

      <!-- Toast Notifications -->
      <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
  </body>
</html>
