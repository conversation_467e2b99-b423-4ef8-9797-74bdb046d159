// Smart Water System JavaScript

// Global variables
let currentUser = null;
let currentScreen = 'home';

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
    setupEventListeners();
});

function initializeApp() {
    // Check if user is logged in
    const savedUser = localStorage.getItem('waterSystemUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        if (currentUser.user_type === 'customer') {
            showDashboard();
        }
    }
    
    // Show home screen by default
    showHome();
}

function setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('login-form');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Register form
    const registerForm = document.getElementById('register-form');
    if (registerForm) {
        registerForm.addEventListener('submit', handleRegister);
    }
    
    // Admin login form
    const adminLoginForm = document.getElementById('admin-login-form');
    if (adminLoginForm) {
        adminLoginForm.addEventListener('submit', handleAdminLogin);
    }
}

// Screen navigation functions
function showScreen(screenId) {
    // Hide all screens
    const screens = document.querySelectorAll('.screen');
    screens.forEach(screen => screen.classList.remove('active'));
    
    // Show target screen
    const targetScreen = document.getElementById(screenId);
    if (targetScreen) {
        targetScreen.classList.add('active');
        currentScreen = screenId.replace('-screen', '');
    }
    
    // Update navigation
    updateNavigation();
}

function showHome() {
    showScreen('home-screen');
}

function showLogin() {
    showScreen('login-screen');
}

function showRegister() {
    showScreen('register-screen');
}

function showDashboard() {
    showScreen('dashboard-screen');
    if (currentUser) {
        loadDashboardData();
    }
}

function showAdminLogin() {
    showScreen('admin-login-screen');
}

function updateNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => link.classList.remove('active'));
    
    // Add active class based on current screen
    const activeLink = document.querySelector(`[onclick="show${currentScreen.charAt(0).toUpperCase() + currentScreen.slice(1)}()"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// API call helper function
async function apiCall(endpoint, method = 'GET', data = null) {
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    try {
        const response = await fetch(`/api${endpoint}`, options);
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('API call failed:', error);
        return { success: false, message: 'Network error occurred' };
    }
}

// Authentication functions
async function handleLogin(event) {
    event.preventDefault();
    showLoading();
    
    const formData = new FormData(event.target);
    const loginData = {
        phone_number: formData.get('phone_number'),
        password: formData.get('password')
    };
    
    try {
        const result = await apiCall('/login', 'POST', loginData);
        hideLoading();
        
        if (result.success) {
            currentUser = result.user;
            localStorage.setItem('waterSystemUser', JSON.stringify(currentUser));
            showToast('Login successful!', 'success');
            showDashboard();
        } else {
            showToast(result.message, 'error');
        }
    } catch (error) {
        hideLoading();
        showToast('Login failed. Please try again.', 'error');
    }
}

async function handleRegister(event) {
    event.preventDefault();
    showLoading();
    
    const formData = new FormData(event.target);
    const registerData = {
        full_name: formData.get('full_name'),
        phone_number: formData.get('phone_number'),
        email: formData.get('email'),
        address: formData.get('address'),
        id_number: formData.get('id_number'),
        password: formData.get('password')
    };
    
    try {
        const result = await apiCall('/register', 'POST', registerData);
        hideLoading();
        
        if (result.success) {
            showToast('Registration successful! Please login.', 'success');
            showLogin();
            // Clear form
            event.target.reset();
        } else {
            showToast(result.message, 'error');
        }
    } catch (error) {
        hideLoading();
        showToast('Registration failed. Please try again.', 'error');
    }
}

async function handleAdminLogin(event) {
    event.preventDefault();
    showLoading();
    
    const formData = new FormData(event.target);
    const loginData = {
        username: formData.get('username'),
        password: formData.get('password')
    };
    
    try {
        // For now, simple admin authentication
        if (loginData.username === 'admin' && loginData.password === 'admin123') {
            hideLoading();
            currentUser = { user_type: 'admin', username: 'admin' };
            localStorage.setItem('waterSystemUser', JSON.stringify(currentUser));
            showToast('Admin login successful!', 'success');
            showAdminDashboard();
        } else {
            hideLoading();
            showToast('Invalid admin credentials', 'error');
        }
    } catch (error) {
        hideLoading();
        showToast('Admin login failed. Please try again.', 'error');
    }
}

function logout() {
    currentUser = null;
    localStorage.removeItem('waterSystemUser');
    showToast('Logged out successfully', 'success');
    showHome();
}

// Dashboard functions
async function loadDashboardData() {
    if (!currentUser) return;
    
    // Update welcome message
    const welcomeElement = document.getElementById('user-welcome');
    if (welcomeElement) {
        welcomeElement.textContent = `Welcome back, ${currentUser.full_name}!`;
    }
    
    // Load user's water usage and billing data
    // This would typically make API calls to get real data
    updateDashboardStats({
        currentUsage: 15.5,
        currentBill: 12750,
        dueDate: '2024-02-15'
    });
}

function updateDashboardStats(data) {
    const usageElement = document.getElementById('current-usage');
    const billElement = document.getElementById('current-bill');
    const dueDateElement = document.getElementById('due-date');
    
    if (usageElement) usageElement.textContent = data.currentUsage;
    if (billElement) billElement.textContent = data.currentBill.toLocaleString();
    if (dueDateElement) dueDateElement.textContent = data.dueDate;
}

// Payment functions
function showPaymentForm() {
    showToast('Payment feature coming soon!', 'info');
}

function viewUsageHistory() {
    showToast('Usage history feature coming soon!', 'info');
}

function reportIssue() {
    showToast('Issue reporting feature coming soon!', 'info');
}

// Admin functions
function showAdminDashboard() {
    showToast('Admin dashboard feature coming soon!', 'info');
}

// Utility functions
function showLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.add('active');
    }
}

function hideLoading() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
        loadingOverlay.classList.remove('active');
    }
}

function showToast(message, type = 'info') {
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) return;
    
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;
    
    toastContainer.appendChild(toast);
    
    // Auto remove toast after 5 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

function getToastIcon(type) {
    switch (type) {
        case 'success': return 'fa-check-circle';
        case 'error': return 'fa-exclamation-circle';
        case 'warning': return 'fa-exclamation-triangle';
        case 'info': return 'fa-info-circle';
        default: return 'fa-info-circle';
    }
}

// Water meter simulation functions (for demo purposes)
function simulateWaterReading() {
    const reading = Math.random() * 100 + 50; // Random reading between 50-150
    return reading.toFixed(2);
}

function simulateLeakDetection() {
    const hasLeak = Math.random() > 0.8; // 20% chance of leak
    return hasLeak;
}

// Mobile responsiveness helpers
function isMobile() {
    return window.innerWidth <= 768;
}

// Initialize PWA features
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then(registration => {
                console.log('SW registered: ', registration);
            })
            .catch(registrationError => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}
