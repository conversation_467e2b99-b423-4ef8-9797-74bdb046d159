// Smart Water System JavaScript

// Global variables
let currentUser = null;
let currentScreen = "home";

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  initializeApp();
  setupEventListeners();
});

function initializeApp() {
  // Check if user is logged in
  const savedUser = localStorage.getItem("waterSystemUser");
  if (savedUser) {
    currentUser = JSON.parse(savedUser);
    if (currentUser.user_type === "customer") {
      showDashboard();
    }
  }

  // Show home screen by default
  showHome();
}

function setupEventListeners() {
  // Login form
  const loginForm = document.getElementById("login-form");
  if (loginForm) {
    loginForm.addEventListener("submit", handleLogin);
  }

  // Register form
  const registerForm = document.getElementById("register-form");
  if (registerForm) {
    registerForm.addEventListener("submit", handleRegister);
  }

  // Admin login form
  const adminLoginForm = document.getElementById("admin-login-form");
  if (adminLoginForm) {
    adminLoginForm.addEventListener("submit", handleAdminLogin);
  }

  // Payment form
  const paymentForm = document.getElementById("payment-form");
  if (paymentForm) {
    paymentForm.addEventListener("submit", handlePayment);
  }
}

// Screen navigation functions
function showScreen(screenId) {
  // Hide all screens
  const screens = document.querySelectorAll(".screen");
  screens.forEach((screen) => screen.classList.remove("active"));

  // Show target screen
  const targetScreen = document.getElementById(screenId);
  if (targetScreen) {
    targetScreen.classList.add("active");
    currentScreen = screenId.replace("-screen", "");
  }

  // Update navigation
  updateNavigation();
}

function showHome() {
  showScreen("home-screen");
}

function showLogin() {
  showScreen("login-screen");
}

function showRegister() {
  showScreen("register-screen");
}

function showDashboard() {
  showScreen("dashboard-screen");
  if (currentUser) {
    loadDashboardData();
  }
}

function showAdminLogin() {
  showScreen("admin-login-screen");
}

function showAdminDashboard() {
  showScreen("admin-dashboard-screen");
  if (currentUser && currentUser.username) {
    loadAdminDashboard();
  }
}

function showPaymentForm() {
  showScreen("payment-screen");
  loadPaymentData();
}

function updateNavigation() {
  const navLinks = document.querySelectorAll(".nav-link");
  navLinks.forEach((link) => link.classList.remove("active"));

  // Add active class based on current screen
  const activeLink = document.querySelector(
    `[onclick="show${
      currentScreen.charAt(0).toUpperCase() + currentScreen.slice(1)
    }()"]`
  );
  if (activeLink) {
    activeLink.classList.add("active");
  }
}

// API call helper function
async function apiCall(endpoint, method = "GET", data = null) {
  const options = {
    method: method,
    headers: {
      "Content-Type": "application/json",
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(`/api${endpoint}`, options);
    const result = await response.json();
    return result;
  } catch (error) {
    console.error("API call failed:", error);
    return { success: false, message: "Network error occurred" };
  }
}

// Authentication functions
async function handleLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    phone_number: formData.get("phone_number"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.user;
      localStorage.setItem("waterSystemUser", JSON.stringify(currentUser));
      showToast("Login successful!", "success");
      showDashboard();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Login failed. Please try again.", "error");
  }
}

async function handleRegister(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const registerData = {
    full_name: formData.get("full_name"),
    phone_number: formData.get("phone_number"),
    email: formData.get("email"),
    address: formData.get("address"),
    id_number: formData.get("id_number"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/register", "POST", registerData);
    hideLoading();

    if (result.success) {
      showToast("Registration successful! Please login.", "success");
      showLogin();
      // Clear form
      event.target.reset();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Registration failed. Please try again.", "error");
  }
}

async function handleAdminLogin(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const loginData = {
    username: formData.get("username"),
    password: formData.get("password"),
  };

  try {
    const result = await apiCall("/admin/login", "POST", loginData);
    hideLoading();

    if (result.success) {
      currentUser = result.admin;
      currentUser.user_type = "admin";
      localStorage.setItem("waterSystemUser", JSON.stringify(currentUser));
      showToast("Admin login successful!", "success");
      showAdminDashboard();
    } else {
      showToast(result.message, "error");
    }
  } catch (error) {
    hideLoading();
    showToast("Admin login failed. Please try again.", "error");
  }
}

function logout() {
  currentUser = null;
  localStorage.removeItem("waterSystemUser");
  showToast("Logged out successfully", "success");
  showHome();
}

// Dashboard functions
async function loadDashboardData() {
  if (!currentUser) return;

  // Update welcome message
  const welcomeElement = document.getElementById("user-welcome");
  if (welcomeElement) {
    welcomeElement.textContent = `Welcome back, ${currentUser.full_name}!`;
  }

  // Load user's water usage and billing data
  // This would typically make API calls to get real data
  updateDashboardStats({
    currentUsage: 15.5,
    currentBill: 12750,
    dueDate: "2024-02-15",
  });
}

function updateDashboardStats(data) {
  const usageElement = document.getElementById("current-usage");
  const billElement = document.getElementById("current-bill");
  const dueDateElement = document.getElementById("due-date");

  if (usageElement) usageElement.textContent = data.currentUsage;
  if (billElement) billElement.textContent = data.currentBill.toLocaleString();
  if (dueDateElement) dueDateElement.textContent = data.dueDate;
}

// Payment functions
function showPaymentForm() {
  showToast("Payment feature coming soon!", "info");
}

function viewUsageHistory() {
  showToast("Usage history feature coming soon!", "info");
}

function reportIssue() {
  showToast("Issue reporting feature coming soon!", "info");
}

// Admin functions
async function loadAdminDashboard() {
  try {
    // Load admin statistics
    const statsResult = await apiCall("/admin/stats", "GET");
    if (statsResult.success) {
      updateAdminStats(statsResult.stats);
    }

    // Load users by default
    showAdminTab("users");
  } catch (error) {
    showToast("Failed to load admin dashboard", "error");
  }
}

function updateAdminStats(stats) {
  const totalUsersElement = document.getElementById("total-users");
  const totalMetersElement = document.getElementById("total-meters");
  const monthlyRevenueElement = document.getElementById("monthly-revenue");
  const activeAlertsElement = document.getElementById("active-alerts");

  if (totalUsersElement) totalUsersElement.textContent = stats.total_users;
  if (totalMetersElement) totalMetersElement.textContent = stats.total_meters;
  if (monthlyRevenueElement)
    monthlyRevenueElement.textContent = stats.monthly_revenue.toLocaleString();
  if (activeAlertsElement)
    activeAlertsElement.textContent = stats.active_alerts;
}

async function showAdminTab(tabName) {
  // Update tab buttons
  const tabButtons = document.querySelectorAll(".tab-btn");
  tabButtons.forEach((btn) => btn.classList.remove("active"));

  const activeTab = document.querySelector(
    `[onclick="showAdminTab('${tabName}')"]`
  );
  if (activeTab) activeTab.classList.add("active");

  // Load content based on tab
  const contentDiv = document.getElementById("admin-content");
  if (!contentDiv) return;

  contentDiv.innerHTML =
    '<div class="loading-table"><i class="fas fa-spinner fa-spin"></i><p>Loading...</p></div>';

  try {
    let result;
    switch (tabName) {
      case "users":
        result = await apiCall("/admin/users", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateUsersTable(result.users);
        }
        break;
      case "meters":
        result = await apiCall("/admin/meters", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateMetersTable(result.meters);
        }
        break;
      case "bills":
        result = await apiCall("/admin/bills", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateBillsTable(result.bills);
        }
        break;
      case "alerts":
        result = await apiCall("/admin/alerts", "GET");
        if (result.success) {
          contentDiv.innerHTML = generateAlertsTable(result.alerts);
        }
        break;
    }
  } catch (error) {
    contentDiv.innerHTML =
      '<div class="alert alert-error">Failed to load data</div>';
  }
}

// Utility functions
function showLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.add("active");
  }
}

function hideLoading() {
  const loadingOverlay = document.getElementById("loading-overlay");
  if (loadingOverlay) {
    loadingOverlay.classList.remove("active");
  }
}

function showToast(message, type = "info") {
  const toastContainer = document.getElementById("toast-container");
  if (!toastContainer) return;

  const toast = document.createElement("div");
  toast.className = `toast ${type}`;
  toast.innerHTML = `
        <div style="display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas ${getToastIcon(type)}"></i>
            <span>${message}</span>
        </div>
    `;

  toastContainer.appendChild(toast);

  // Auto remove toast after 5 seconds
  setTimeout(() => {
    if (toast.parentNode) {
      toast.parentNode.removeChild(toast);
    }
  }, 5000);
}

function getToastIcon(type) {
  switch (type) {
    case "success":
      return "fa-check-circle";
    case "error":
      return "fa-exclamation-circle";
    case "warning":
      return "fa-exclamation-triangle";
    case "info":
      return "fa-info-circle";
    default:
      return "fa-info-circle";
  }
}

// Water meter simulation functions (for demo purposes)
function simulateWaterReading() {
  const reading = Math.random() * 100 + 50; // Random reading between 50-150
  return reading.toFixed(2);
}

function simulateLeakDetection() {
  const hasLeak = Math.random() > 0.8; // 20% chance of leak
  return hasLeak;
}

// Mobile responsiveness helpers
function isMobile() {
  return window.innerWidth <= 768;
}

// Initialize PWA features
if ("serviceWorker" in navigator) {
  window.addEventListener("load", () => {
    navigator.serviceWorker
      .register("/sw.js")
      .then((registration) => {
        console.log("SW registered: ", registration);
      })
      .catch((registrationError) => {
        console.log("SW registration failed: ", registrationError);
      });
  });
}

// Table generation functions
function generateUsersTable(users) {
  if (users.length === 0) {
    return '<div class="alert alert-info">No customers found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Phone</th>
          <th>Email</th>
          <th>Meters</th>
          <th>Status</th>
          <th>Joined</th>
        </tr>
      </thead>
      <tbody>
  `;

  users.forEach((user) => {
    html += `
      <tr>
        <td>${user.full_name}</td>
        <td>${user.phone_number}</td>
        <td>${user.email || "N/A"}</td>
        <td>${user.meter_count}</td>
        <td><span class="status-badge ${
          user.is_active ? "status-active" : "status-pending"
        }">${user.is_active ? "Active" : "Inactive"}</span></td>
        <td>${new Date(user.created_at).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generateMetersTable(meters) {
  if (meters.length === 0) {
    return '<div class="alert alert-info">No meters found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Meter Number</th>
          <th>Customer</th>
          <th>Location</th>
          <th>Last Reading</th>
          <th>Status</th>
          <th>Installed</th>
        </tr>
      </thead>
      <tbody>
  `;

  meters.forEach((meter) => {
    html += `
      <tr>
        <td>${meter.meter_number}</td>
        <td>${meter.customer_name}</td>
        <td>${meter.location}</td>
        <td>${meter.last_reading} m³</td>
        <td><span class="status-badge status-active">${meter.status}</span></td>
        <td>${new Date(meter.installation_date).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generateBillsTable(bills) {
  if (bills.length === 0) {
    return '<div class="alert alert-info">No bills found</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Customer</th>
          <th>Period</th>
          <th>Consumption</th>
          <th>Amount</th>
          <th>Due Date</th>
          <th>Status</th>
        </tr>
      </thead>
      <tbody>
  `;

  bills.forEach((bill) => {
    html += `
      <tr>
        <td>${bill.customer_name}</td>
        <td>${bill.billing_period}</td>
        <td>${bill.consumption} m³</td>
        <td>${bill.amount.toLocaleString()} RWF</td>
        <td>${new Date(bill.due_date).toLocaleDateString()}</td>
        <td><span class="status-badge ${
          bill.status === "paid" ? "status-paid" : "status-pending"
        }">${bill.status}</span></td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

function generateAlertsTable(alerts) {
  if (alerts.length === 0) {
    return '<div class="alert alert-success">No active alerts</div>';
  }

  let html = `
    <table class="data-table">
      <thead>
        <tr>
          <th>Customer</th>
          <th>Meter</th>
          <th>Alert Type</th>
          <th>Severity</th>
          <th>Description</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
  `;

  alerts.forEach((alert) => {
    html += `
      <tr>
        <td>${alert.customer_name}</td>
        <td>${alert.meter_number}</td>
        <td>${alert.alert_type}</td>
        <td><span class="status-badge status-high">${alert.severity}</span></td>
        <td>${alert.description}</td>
        <td>${new Date(alert.created_at).toLocaleDateString()}</td>
      </tr>
    `;
  });

  html += "</tbody></table>";
  return html;
}

// Payment functions
function loadPaymentData() {
  // Load current bill data for payment
  const billConsumption = document.getElementById("bill-consumption");
  const billTotal = document.getElementById("bill-total");
  const amountPayment = document.getElementById("amount_payment");

  // Sample data - in real app, this would come from API
  const consumption = 15.5;
  const waterCost = consumption * 500; // 500 RWF per m³
  const serviceFee = 1000;
  const total = waterCost + serviceFee;

  if (billConsumption) billConsumption.textContent = `${consumption} m³`;
  if (billTotal) billTotal.textContent = `${total.toLocaleString()} RWF`;
  if (amountPayment) amountPayment.value = total;
}

async function handlePayment(event) {
  event.preventDefault();
  showLoading();

  const formData = new FormData(event.target);
  const paymentData = {
    bill_id: 1, // This would be dynamic in real app
    user_id: currentUser ? currentUser.id : 1,
    amount: parseFloat(formData.get("amount")),
    payment_method: formData.get("payment_method"),
    phone_number: formData.get("phone_number"),
  };

  try {
    // Simulate payment processing
    await new Promise((resolve) => setTimeout(resolve, 2000));

    hideLoading();
    showToast("Payment processed successfully!", "success");
    showDashboard();

    // In real app, you would call:
    // const result = await apiCall('/payment/process', 'POST', paymentData);
  } catch (error) {
    hideLoading();
    showToast("Payment failed. Please try again.", "error");
  }
}
