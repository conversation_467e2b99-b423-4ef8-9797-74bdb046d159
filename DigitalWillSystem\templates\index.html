<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Digital Will & Inheritance System - Rwanda</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#8b5cf6" />
    <meta name="description" content="Secure digital will creation and inheritance management system for Rwanda" />
    
    <!-- PWA Meta Tags -->
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/icon-192.png') }}" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Digital Will System" />
  </head>
  <body>
    <div id="app">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <i class="fas fa-scroll"></i>
          <span>Digital Will System</span>
        </div>
        <div class="nav-links">
          <a href="#" class="nav-link" onclick="showHome()">
            <i class="fas fa-home"></i> Home
          </a>
          <a href="#" class="nav-link" onclick="showLogin()">
            <i class="fas fa-sign-in-alt"></i> Login
          </a>
          <a href="#" class="nav-link" onclick="showRegister()">
            <i class="fas fa-user-plus"></i> Register
          </a>
          <a href="#" class="nav-link" onclick="showAdminLogin()">
            <i class="fas fa-cog"></i> Admin
          </a>
        </div>
      </nav>

      <!-- Home Screen -->
      <div id="home-screen" class="screen active">
        <div class="hero-section">
          <div class="hero-content">
            <h1>
              <i class="fas fa-scroll"></i>
              Digital Will & Inheritance System
            </h1>
            <p class="hero-subtitle">
              Secure, Legal, and Transparent Digital Wills for Rwanda
            </p>
            <p class="hero-description">
              Create legally binding digital wills with digital witnesses and legal validation. 
              Prevent inheritance disputes and ensure your legacy is protected.
            </p>
          </div>

          <div class="hero-stats">
            <div class="stat-item">
              <i class="fas fa-shield-alt"></i>
              <h3>100% Secure</h3>
              <p>Encrypted Storage</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-balance-scale"></i>
              <h3>Legal Validation</h3>
              <p>Certified Lawyers</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-users"></i>
              <h3>Digital Witnesses</h3>
              <p>Verified Identity</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-mobile-alt"></i>
              <h3>24/7 Access</h3>
              <p>Anytime, Anywhere</p>
            </div>
          </div>
        </div>

        <div class="action-cards">
          <div class="action-card" onclick="showLogin()">
            <i class="fas fa-file-signature"></i>
            <h3>Create Your Will</h3>
            <p>Secure digital will creation with legal templates</p>
            <span class="card-price">25,000 RWF</span>
          </div>

          <div class="action-card" onclick="showRegister()">
            <i class="fas fa-user-plus"></i>
            <h3>New User Registration</h3>
            <p>Join thousands protecting their legacy</p>
            <span class="card-price">Free Registration</span>
          </div>

          <div class="action-card" onclick="showAdminLogin()">
            <i class="fas fa-cogs"></i>
            <h3>Legal Validator Portal</h3>
            <p>For certified lawyers and legal professionals</p>
            <span class="card-price">Professional Access</span>
          </div>
        </div>

        <div class="features-section">
          <h2>Why Choose Digital Will System?</h2>
          <div class="features-grid">
            <div class="feature-item">
              <i class="fas fa-lock"></i>
              <h4>Secure & Encrypted</h4>
              <p>Bank-level security with end-to-end encryption</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-certificate"></i>
              <h4>Legal Compliance</h4>
              <p>Compliant with Rwandan inheritance laws</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-eye-slash"></i>
              <h4>Privacy Protected</h4>
              <p>Your will remains private until needed</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-clock"></i>
              <h4>Quick Process</h4>
              <p>Create your will in under 30 minutes</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-handshake"></i>
              <h4>Digital Witnesses</h4>
              <p>Verified digital witnesses for authenticity</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-gavel"></i>
              <h4>Legal Validation</h4>
              <p>Optional validation by certified lawyers</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Login Screen -->
      <div id="login-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-sign-in-alt"></i> User Login</h2>
            <p>Access your digital will dashboard</p>
          </div>

          <form id="login-form" class="form">
            <div class="form-group">
              <label for="login_phone">Phone Number</label>
              <input
                type="tel"
                id="login_phone"
                name="phone_number"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div class="form-group">
              <label for="login_password">Password</label>
              <input
                type="password"
                id="login_password"
                name="password"
                placeholder="Enter your password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Login
              </button>
            </div>
          </form>

          <div class="form-footer">
            <p>Don't have an account? <a href="#" onclick="showRegister()">Register here</a></p>
          </div>
        </div>
      </div>

      <!-- Register Screen -->
      <div id="register-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-user-plus"></i> User Registration</h2>
            <p>Create your secure digital will account</p>
          </div>

          <form id="register-form" class="form">
            <div class="form-row">
              <div class="form-group">
                <label for="full_name">Full Name</label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div class="form-group">
                <label for="national_id">National ID</label>
                <input
                  type="text"
                  id="national_id"
                  name="national_id"
                  placeholder="Enter your National ID"
                  required
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="phone_number">Phone Number</label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  placeholder="Enter your phone number"
                  required
                />
              </div>
              <div class="form-group">
                <label for="email">Email Address</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="Enter your email"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="date_of_birth">Date of Birth</label>
                <input
                  type="date"
                  id="date_of_birth"
                  name="date_of_birth"
                  required
                />
              </div>
              <div class="form-group">
                <label for="password">Password</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  placeholder="Create a strong password"
                  required
                />
              </div>
            </div>

            <div class="form-group">
              <label for="address">Address</label>
              <textarea
                id="address"
                name="address"
                placeholder="Enter your full address"
                rows="3"
                required
              ></textarea>
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> Back
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> Register
              </button>
            </div>
          </form>

          <div class="form-footer">
            <p>Already have an account? <a href="#" onclick="showLogin()">Login here</a></p>
          </div>
        </div>
      </div>

      <!-- Loading Overlay -->
      <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p>Processing...</p>
        </div>
      </div>

      <!-- Toast Notifications -->
      <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
  </body>
</html>
