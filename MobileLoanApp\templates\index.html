<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Mobile Loan Scoring App - Rwanda</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}" />
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#10b981" />
    <meta name="description" content="Smart loan scoring for informal workers using mobile money data" />
    
    <!-- PWA Meta Tags -->
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/icon-192.png') }}" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Mobile Loan App" />
  </head>
  <body>
    <div id="app">
      <!-- Navigation -->
      <nav class="navbar">
        <div class="nav-brand">
          <i class="fas fa-mobile-alt"></i>
          <span data-translate="app_title">Mobile Loan App</span>
        </div>
        <div class="nav-links">
          <a href="#" class="nav-link" onclick="showHome()">
            <i class="fas fa-home"></i> <span data-translate="home">Home</span>
          </a>
          <a href="#" class="nav-link" onclick="showLogin()">
            <i class="fas fa-sign-in-alt"></i> <span data-translate="login">Login</span>
          </a>
          <a href="#" class="nav-link" onclick="showRegister()">
            <i class="fas fa-user-plus"></i> <span data-translate="register">Register</span>
          </a>
          <a href="#" class="nav-link" onclick="showAdminLogin()">
            <i class="fas fa-cog"></i> <span data-translate="admin">Admin</span>
          </a>
        </div>
      </nav>

      <!-- Home Screen -->
      <div id="home-screen" class="screen active">
        <div class="hero-section">
          <div class="hero-content">
            <h1>
              <i class="fas fa-mobile-alt"></i>
              <span data-translate="system_title">Mobile Loan Scoring App</span>
            </h1>
            <p class="hero-subtitle" data-translate="hero_subtitle">
              Smart Loans for Informal Workers in Rwanda
            </p>
            <p class="hero-description" data-translate="hero_description">
              Get instant loans based on your mobile money history, airtime purchases, and savings behavior. 
              No collateral required - your digital footprint is your credit score.
            </p>
          </div>

          <div class="hero-stats">
            <div class="stat-item">
              <i class="fas fa-clock"></i>
              <h3 data-translate="instant_approval">Instant Approval</h3>
              <p data-translate="under_5_minutes">Under 5 Minutes</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-shield-alt"></i>
              <h3 data-translate="no_collateral">No Collateral</h3>
              <p data-translate="mobile_data_only">Mobile Data Only</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-chart-line"></i>
              <h3 data-translate="smart_scoring">Smart Scoring</h3>
              <p data-translate="ai_powered">AI Powered</p>
            </div>
            <div class="stat-item">
              <i class="fas fa-money-bill-wave"></i>
              <h3 data-translate="flexible_terms">Flexible Terms</h3>
              <p data-translate="up_to_500k">Up to 500K RWF</p>
            </div>
          </div>
        </div>

        <div class="action-cards">
          <div class="action-card" onclick="showLogin()">
            <i class="fas fa-calculator"></i>
            <h3 data-translate="check_score">Check Your Credit Score</h3>
            <p data-translate="check_score_desc">See your credit score based on mobile money activity</p>
            <span class="card-price" data-translate="free">Free</span>
          </div>

          <div class="action-card" onclick="showRegister()">
            <i class="fas fa-hand-holding-usd"></i>
            <h3 data-translate="apply_loan">Apply for Loan</h3>
            <p data-translate="apply_loan_desc">Get instant loan approval up to 500,000 RWF</p>
            <span class="card-price">10K - 500K RWF</span>
          </div>

          <div class="action-card" onclick="showRegister()">
            <i class="fas fa-user-plus"></i>
            <h3 data-translate="join_now">Join Now</h3>
            <p data-translate="join_now_desc">Register and start building your credit score</p>
            <span class="card-price" data-translate="free_registration">Free Registration</span>
          </div>
        </div>

        <div class="features-section">
          <h2 data-translate="how_it_works">How It Works</h2>
          <div class="features-grid">
            <div class="feature-item">
              <i class="fas fa-mobile-alt"></i>
              <h4 data-translate="sync_data">1. Sync Mobile Data</h4>
              <p data-translate="sync_data_desc">Connect your MTN/Airtel mobile money account</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-chart-bar"></i>
              <h4 data-translate="get_score">2. Get Credit Score</h4>
              <p data-translate="get_score_desc">AI analyzes your transaction patterns</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-hand-holding-usd"></i>
              <h4 data-translate="apply_instantly">3. Apply Instantly</h4>
              <p data-translate="apply_instantly_desc">Get approved in under 5 minutes</p>
            </div>
            <div class="feature-item">
              <i class="fas fa-money-check-alt"></i>
              <h4 data-translate="receive_funds">4. Receive Funds</h4>
              <p data-translate="receive_funds_desc">Money sent directly to your mobile wallet</p>
            </div>
          </div>
        </div>

        <div class="loan-calculator">
          <h2 data-translate="loan_calculator">Loan Calculator</h2>
          <div class="calculator-form">
            <div class="form-group">
              <label data-translate="loan_amount">Loan Amount (RWF)</label>
              <input type="range" id="loanAmount" min="10000" max="500000" value="100000" oninput="updateCalculator()">
              <span id="loanAmountDisplay">100,000 RWF</span>
            </div>
            <div class="form-group">
              <label data-translate="loan_term">Loan Term (Days)</label>
              <input type="range" id="loanTerm" min="7" max="90" value="30" oninput="updateCalculator()">
              <span id="loanTermDisplay">30 days</span>
            </div>
            <div class="calculator-results">
              <div class="result-item">
                <span data-translate="interest_rate">Interest Rate:</span>
                <span id="interestRate">15%</span>
              </div>
              <div class="result-item">
                <span data-translate="total_repayment">Total Repayment:</span>
                <span id="totalRepayment">115,000 RWF</span>
              </div>
              <div class="result-item">
                <span data-translate="daily_payment">Daily Payment:</span>
                <span id="dailyPayment">3,833 RWF</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Login Screen -->
      <div id="login-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-sign-in-alt"></i> <span data-translate="user_login">User Login</span></h2>
            <p data-translate="user_login_desc">Access your loan dashboard</p>
          </div>

          <form id="login-form" class="form">
            <div class="form-group">
              <label for="login_phone" data-translate="phone_number">Phone Number</label>
              <input
                type="tel"
                id="login_phone"
                name="phone_number"
                data-translate-placeholder="enter_phone"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div class="form-group">
              <label for="login_password" data-translate="password">Password</label>
              <input
                type="password"
                id="login_password"
                name="password"
                data-translate-placeholder="enter_password"
                placeholder="Enter your password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> <span data-translate="login">Login</span>
              </button>
            </div>
          </form>

          <div class="form-footer">
            <p><span data-translate="dont_have_account">Don't have an account?</span> <a href="#" onclick="showRegister()"><span data-translate="register_here">Register here</span></a></p>
          </div>
        </div>
      </div>

      <!-- Register Screen -->
      <div id="register-screen" class="screen">
        <div class="form-container">
          <div class="form-header">
            <h2><i class="fas fa-user-plus"></i> <span data-translate="user_registration">User Registration</span></h2>
            <p data-translate="user_registration_desc">Create your loan account</p>
          </div>

          <form id="register-form" class="form">
            <div class="form-row">
              <div class="form-group">
                <label for="full_name" data-translate="full_name">Full Name</label>
                <input
                  type="text"
                  id="full_name"
                  name="full_name"
                  data-translate-placeholder="enter_full_name"
                  placeholder="Enter your full name"
                  required
                />
              </div>
              <div class="form-group">
                <label for="national_id" data-translate="national_id">National ID</label>
                <input
                  type="text"
                  id="national_id"
                  name="national_id"
                  data-translate-placeholder="enter_national_id"
                  placeholder="Enter your National ID"
                  required
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="phone_number" data-translate="phone_number">Phone Number</label>
                <input
                  type="tel"
                  id="phone_number"
                  name="phone_number"
                  data-translate-placeholder="enter_phone_number"
                  placeholder="Enter your phone number"
                  required
                />
              </div>
              <div class="form-group">
                <label for="date_of_birth" data-translate="date_of_birth">Date of Birth</label>
                <input
                  type="date"
                  id="date_of_birth"
                  name="date_of_birth"
                  required
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="occupation" data-translate="occupation">Occupation</label>
                <select id="occupation" name="occupation" required>
                  <option value="">Select occupation</option>
                  <option value="trader">Trader</option>
                  <option value="farmer">Farmer</option>
                  <option value="driver">Driver</option>
                  <option value="artisan">Artisan</option>
                  <option value="vendor">Vendor</option>
                  <option value="other">Other</option>
                </select>
              </div>
              <div class="form-group">
                <label for="monthly_income" data-translate="monthly_income">Monthly Income (RWF)</label>
                <input
                  type="number"
                  id="monthly_income"
                  name="monthly_income"
                  data-translate-placeholder="enter_monthly_income"
                  placeholder="Enter estimated monthly income"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="address" data-translate="address">Address</label>
              <textarea
                id="address"
                name="address"
                data-translate-placeholder="enter_address"
                placeholder="Enter your full address"
                rows="3"
                required
              ></textarea>
            </div>

            <div class="form-group">
              <label for="password" data-translate="password">Password</label>
              <input
                type="password"
                id="password"
                name="password"
                data-translate-placeholder="create_password"
                placeholder="Create a strong password"
                required
              />
            </div>

            <div class="form-actions">
              <button type="button" class="btn btn-secondary" onclick="showHome()">
                <i class="fas fa-arrow-left"></i> <span data-translate="back">Back</span>
              </button>
              <button type="submit" class="btn btn-primary">
                <i class="fas fa-user-plus"></i> <span data-translate="register">Register</span>
              </button>
            </div>
          </form>

          <div class="form-footer">
            <p><span data-translate="already_have_account">Already have an account?</span> <a href="#" onclick="showLogin()"><span data-translate="login_here">Login here</span></a></p>
          </div>
        </div>
      </div>

      <!-- Language Selector -->
      <div class="language-selector">
        <button class="language-btn active" onclick="changeLanguage('en')">
          <i class="fas fa-globe"></i> EN
        </button>
        <button class="language-btn" onclick="changeLanguage('fr')">
          <i class="fas fa-globe"></i> FR
        </button>
        <button class="language-btn" onclick="changeLanguage('rw')">
          <i class="fas fa-globe"></i> RW
        </button>
      </div>

      <!-- Loading Overlay -->
      <div id="loading-overlay" class="loading-overlay">
        <div class="loading-spinner">
          <i class="fas fa-spinner fa-spin"></i>
          <p data-translate="processing">Processing...</p>
        </div>
      </div>

      <!-- Toast Notifications -->
      <div id="toast-container" class="toast-container"></div>
    </div>

    <!-- JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
  </body>
</html>
